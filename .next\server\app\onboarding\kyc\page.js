(()=>{var e={};e.id=868,e.ids=[868],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},8893:e=>{"use strict";e.exports=require("buffer")},4770:e=>{"use strict";e.exports=require("crypto")},7702:e=>{"use strict";e.exports=require("events")},2615:e=>{"use strict";e.exports=require("http")},8791:e=>{"use strict";e.exports=require("https")},8216:e=>{"use strict";e.exports=require("net")},8621:e=>{"use strict";e.exports=require("punycode")},6162:e=>{"use strict";e.exports=require("stream")},2452:e=>{"use strict";e.exports=require("tls")},7360:e=>{"use strict";e.exports=require("url")},1568:e=>{"use strict";e.exports=require("zlib")},6953:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d}),t(3308),t(291),t(5866);var a=t(3191),s=t(8716),i=t(7922),n=t.n(i),l=t(5231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(r,o);let d=["",{children:["onboarding",{children:["kyc",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,3308)),"C:\\xampp\\htdocs\\aureus_africa\\app\\onboarding\\kyc\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,291)),"C:\\xampp\\htdocs\\aureus_africa\\app\\onboarding\\layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,5866,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\xampp\\htdocs\\aureus_africa\\app\\onboarding\\kyc\\page.tsx"],u="/onboarding/kyc/page",m={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/onboarding/kyc/page",pathname:"/onboarding/kyc",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},4542:(e,r,t)=>{Promise.resolve().then(t.bind(t,8528))},7554:(e,r,t)=>{Promise.resolve().then(t.bind(t,1745))},8528:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var a=t(326),s=t(7577),i=t(5047),n=t(9240),l=t(1579),o=t(286);function d(){let{user:e,refreshUser:r}=(0,n.a)(),t=(0,i.useRouter)(),[d,c]=(0,s.useState)(!1),[u,m]=(0,s.useState)(null),[p,x]=(0,s.useState)({firstName:e?.telegram_profile?.first_name||"",lastName:e?.telegram_profile?.last_name||"",idType:"national_id",idNumber:"",phoneNumber:"",emailAddress:"",address:"",city:"",postalCode:"",acceptedPrivacy:!1}),b=e=>{let{name:r,value:t,type:a}=e.target,s="checkbox"===a?e.target.checked:void 0;x(e=>({...e,[r]:"checkbox"===a?s:t}))},g=async a=>{if(a.preventDefault(),!p.acceptedPrivacy){m("You must accept the privacy policy to continue");return}if(!e?.telegram_profile?.id){m("User profile not found");return}c(!0),m(null);try{let{error:a}=await l.OQ.from("kyc_information").insert({user_id:e.telegram_profile.id,first_name:p.firstName,last_name:p.lastName,id_type:p.idType,id_number_encrypted:p.idNumber,id_number_hash:p.idNumber,phone_number:p.phoneNumber,email_address:p.emailAddress,street_address:p.address,city:p.city,postal_code:p.postalCode,country_code:e.telegram_profile.country||"ZA",country_name:e.telegram_profile.country||"South Africa",data_consent_given:p.acceptedPrivacy,privacy_policy_accepted:p.acceptedPrivacy,kyc_status:"pending",created_by_telegram_id:e.telegram_profile.telegram_id});if(a)throw Error(a.message);await r(),t.push("/dashboard")}catch(e){console.error("Error submitting KYC:",e),m("Failed to submit KYC information. Please try again.")}finally{c(!1)}},h=!!e?.telegram_profile?.country&&(0,o.f6)(e.telegram_profile.country);return(0,a.jsxs)("div",{className:"p-8",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-800",children:"KYC Verification"}),a.jsx("p",{className:"text-gray-600 mt-2",children:"Please provide your information for verification"})]}),(0,a.jsxs)("form",{onSubmit:g,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg border border-gray-200 mb-6",children:[a.jsx("h2",{className:"text-lg font-medium text-gray-800 mb-4",children:"Personal Information"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-gray-700 text-sm font-medium mb-1",children:"First Name"}),a.jsx("input",{type:"text",name:"firstName",value:p.firstName,onChange:b,required:!0,className:"w-full p-2 border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-gray-700 text-sm font-medium mb-1",children:"Last Name"}),a.jsx("input",{type:"text",name:"lastName",value:p.lastName,onChange:b,required:!0,className:"w-full p-2 border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-gray-700 text-sm font-medium mb-1",children:"ID Type"}),(0,a.jsxs)("select",{name:"idType",value:p.idType,onChange:b,required:!0,className:"w-full p-2 border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500",children:[a.jsx("option",{value:"national_id",children:"National ID"}),a.jsx("option",{value:"passport",children:"Passport"}),a.jsx("option",{value:"drivers_license",children:"Driver's License"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-gray-700 text-sm font-medium mb-1",children:"ID Number"}),a.jsx("input",{type:"text",name:"idNumber",value:p.idNumber,onChange:b,required:!0,className:"w-full p-2 border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"})]})]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg border border-gray-200 mb-6",children:[a.jsx("h2",{className:"text-lg font-medium text-gray-800 mb-4",children:"Contact Information"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-gray-700 text-sm font-medium mb-1",children:"Phone Number"}),a.jsx("input",{type:"tel",name:"phoneNumber",value:p.phoneNumber,onChange:b,required:!0,className:"w-full p-2 border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-gray-700 text-sm font-medium mb-1",children:"Email Address"}),a.jsx("input",{type:"email",name:"emailAddress",value:p.emailAddress,onChange:b,required:!0,className:"w-full p-2 border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"})]})]})]}),h&&(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg border border-gray-200 mb-6",children:[a.jsx("h2",{className:"text-lg font-medium text-gray-800 mb-4",children:"Address Information"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-gray-700 text-sm font-medium mb-1",children:"Address"}),a.jsx("textarea",{name:"address",value:p.address,onChange:b,required:!0,className:"w-full p-2 border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500",rows:2})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-gray-700 text-sm font-medium mb-1",children:"City"}),a.jsx("input",{type:"text",name:"city",value:p.city,onChange:b,required:!0,className:"w-full p-2 border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-gray-700 text-sm font-medium mb-1",children:"Postal Code"}),a.jsx("input",{type:"text",name:"postalCode",value:p.postalCode,onChange:b,required:!0,className:"w-full p-2 border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"})]})]})]})]}),a.jsx("div",{className:"mb-6",children:(0,a.jsxs)("label",{className:"flex items-start cursor-pointer",children:[a.jsx("input",{type:"checkbox",name:"acceptedPrivacy",checked:p.acceptedPrivacy,onChange:b,className:"mt-1 h-5 w-5 text-blue-600 rounded border-gray-300 focus:ring-blue-500"}),a.jsx("span",{className:"ml-2 text-gray-700 text-sm",children:"I consent to the collection and processing of my personal information in accordance with Aureus Alliance Holdings' Privacy Policy and applicable data protection laws."})]})}),u&&a.jsx("div",{className:"mb-6 p-3 bg-red-100 text-red-700 rounded-md text-sm",children:u}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("button",{type:"button",onClick:()=>t.push("/onboarding/country"),className:"px-6 py-2 bg-gray-300 hover:bg-gray-400 text-gray-800 rounded-lg transition-colors duration-200",children:"Back"}),a.jsx("button",{type:"submit",disabled:d||!p.acceptedPrivacy,className:`px-6 py-2 rounded-lg transition-colors duration-200 ${d||!p.acceptedPrivacy?"bg-blue-300 text-white cursor-not-allowed":"bg-blue-600 hover:bg-blue-700 text-white"}`,children:d?"Processing...":"Submit & Continue"})]})]})]})}},1745:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var a=t(326),s=t(7577),i=t(5047),n=t(9240),l=t(3167);let o=s.forwardRef(function({title:e,titleId:r,...t},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},t),e?s.createElement("title",{id:r},e):null,s.createElement("path",{fillRule:"evenodd",d:"M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25ZM12.75 6a.75.75 0 0 0-1.5 0v6c0 .414.336.75.75.75h4.5a.75.75 0 0 0 0-1.5h-3.75V6Z",clipRule:"evenodd"}))});function d({steps:e,currentStep:r,totalSteps:t}){let s=r/t*100;return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[a.jsx("h2",{className:"text-lg font-semibold text-gray-800",children:"Setup Progress"}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:[r," of ",t," completed"]})]}),a.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:a.jsx("div",{className:"bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-300 ease-out",style:{width:`${s}%`}})})]}),a.jsx("div",{className:"space-y-3",children:e.map((e,r)=>(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[a.jsx("div",{className:"flex-shrink-0 mt-1",children:e.completed?a.jsx(l.Z,{className:"h-5 w-5 text-green-500"}):e.current?a.jsx(o,{className:"h-5 w-5 text-blue-500"}):a.jsx("div",{className:"h-5 w-5 rounded-full border-2 border-gray-300"})}),(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("h3",{className:`text-sm font-medium ${e.completed?"text-green-700":e.current?"text-blue-700":"text-gray-500"}`,children:e.title}),a.jsx("p",{className:`text-xs ${e.completed?"text-green-600":e.current?"text-blue-600":"text-gray-400"}`,children:e.description})]})]},e.id))})]})}var c=t(668);function u({children:e}){let{user:r,checkOnboardingStatus:t}=(0,n.a)();(0,i.useRouter)();let l=(0,i.usePathname)(),[o,u]=(0,s.useState)({hasAcceptedTerms:!1,hasSelectedCountry:!1,hasCompletedKYC:!1}),[m,p]=(0,s.useState)(!0);if(m)return a.jsx("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-yellow-400 mx-auto mb-4"}),a.jsx("p",{className:"text-white text-lg",children:"Loading onboarding..."})]})});let x=[{id:"terms",title:"Accept Terms & Conditions",description:"Review and accept our terms of service",completed:o.hasAcceptedTerms,current:"/onboarding/terms"===l},{id:"country",title:"Select Country",description:"Choose your country of residence",completed:o.hasSelectedCountry,current:"/onboarding/country"===l},{id:"kyc",title:"KYC Verification",description:"Provide your identification details",completed:o.hasCompletedKYC,current:"/onboarding/kyc"===l},{id:"complete",title:"Setup Complete",description:"Access your dashboard",completed:o.hasAcceptedTerms&&o.hasSelectedCountry&&o.hasCompletedKYC,current:!1}];x.findIndex(e=>e.current);let b=x.filter(e=>e.completed).length;return a.jsx(c.Z,{children:a.jsx("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900",children:a.jsx("div",{className:"container mx-auto px-4 py-8",children:(0,a.jsxs)("div",{className:"max-w-2xl mx-auto",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[a.jsx("h1",{className:"text-3xl font-bold text-white mb-2",children:"Welcome to Aureus Alliance"}),a.jsx("p",{className:"text-gray-300",children:"Let's get your account set up in just a few steps"})]}),a.jsx(d,{steps:x,currentStep:b,totalSteps:x.length}),a.jsx("div",{className:"bg-white rounded-lg shadow-xl overflow-hidden",children:e}),a.jsx("div",{className:"text-center mt-6",children:(0,a.jsxs)("p",{className:"text-gray-300 text-sm",children:["Need help? Contact us at"," ",a.jsx("a",{href:"https://t.me/AureusAllianceBot",target:"_blank",rel:"noopener noreferrer",className:"text-yellow-400 hover:text-yellow-300 underline",children:"@AureusAllianceBot"})]})})]})})})})}},286:(e,r,t)=>{"use strict";t.d(r,{B_:()=>d,f6:()=>o,fj:()=>l,od:()=>a,pD:()=>s});let a=[{code:"ZA",name:"South Africa",region:"africa"},{code:"SZ",name:"Eswatini",region:"africa"},{code:"NA",name:"Namibia",region:"africa"},{code:"BW",name:"Botswana",region:"africa"},{code:"ZW",name:"Zimbabwe",region:"africa"},{code:"MZ",name:"Mozambique",region:"africa"},{code:"LS",name:"Lesotho",region:"africa"},{code:"US",name:"United States",region:"western"},{code:"GB",name:"United Kingdom",region:"western"},{code:"CA",name:"Canada",region:"western"},{code:"AU",name:"Australia",region:"western"},{code:"NZ",name:"New Zealand",region:"western"},{code:"SG",name:"Singapore",region:"asia"},{code:"AE",name:"United Arab Emirates",region:"asia"},{code:"OTHER",name:"Other Country",region:"other"}],s=e=>{let r=a.find(r=>r.code===e);return r?r.name:"Unknown Country"},i=e=>["ZA","SZ","NA"].includes(e),n=e=>!0,l=e=>{let r=[];return n(e)&&r.push("USDT"),i(e)&&r.push("ZAR"),r},o=e=>["US","GB","CA"].includes(e),d=e=>i(e)?"You can pay using ZAR bank transfer or USDT cryptocurrency.":"You can pay using USDT cryptocurrency on multiple networks."},3308:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});let a=(0,t(8570).createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\app\onboarding\kyc\page.tsx#default`)},291:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});let a=(0,t(8570).createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\app\onboarding\layout.tsx#default`)},3167:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});var a=t(7577);let s=a.forwardRef(function({title:e,titleId:r,...t},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},t),e?a.createElement("title",{id:r},e):null,a.createElement("path",{fillRule:"evenodd",d:"M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm13.36-1.814a.75.75 0 1 0-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 0 0-1.06 1.06l2.25 2.25a.75.75 0 0 0 1.14-.094l3.75-5.25Z",clipRule:"evenodd"}))})}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[276,471,529,585],()=>t(6953));module.exports=a})();