(()=>{var e={};e.id=180,e.ids=[180],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},8893:e=>{"use strict";e.exports=require("buffer")},4770:e=>{"use strict";e.exports=require("crypto")},7702:e=>{"use strict";e.exports=require("events")},2615:e=>{"use strict";e.exports=require("http")},8791:e=>{"use strict";e.exports=require("https")},8216:e=>{"use strict";e.exports=require("net")},8621:e=>{"use strict";e.exports=require("punycode")},6162:e=>{"use strict";e.exports=require("stream")},2452:e=>{"use strict";e.exports=require("tls")},7360:e=>{"use strict";e.exports=require("url")},1568:e=>{"use strict";e.exports=require("zlib")},7116:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>o,routeModule:()=>x,tree:()=>c}),s(4276),s(3321),s(5866);var r=s(3191),a=s(8716),n=s(7922),l=s.n(n),i=s(5231),d={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);s.d(t,d);let c=["",{children:["dashboard",{children:["kyc",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,4276)),"C:\\xampp\\htdocs\\aureus_africa\\app\\dashboard\\kyc\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,3321)),"C:\\xampp\\htdocs\\aureus_africa\\app\\dashboard\\layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(s.t.bind(s,5866,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\xampp\\htdocs\\aureus_africa\\app\\dashboard\\kyc\\page.tsx"],u="/dashboard/kyc/page",m={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/dashboard/kyc/page",pathname:"/dashboard/kyc",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},4661:(e,t,s)=>{Promise.resolve().then(s.bind(s,5086))},5086:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>g});var r=s(326),a=s(7577),n=s(9240),l=s(1579),i=s(3679),d=s(1021);function c({requiredDocuments:e,existingDocuments:t,onUpload:s,uploading:n}){let[l,c]=(0,a.useState)(null),[o,u]=(0,a.useState)({}),m=(0,a.useRef)({}),x=(e,t)=>{if(!["image/jpeg","image/jpg","image/png","application/pdf"].includes(t.type)){alert("Please upload only JPG, PNG, or PDF files");return}if(t.size>10485760){alert("File size must be less than 10MB");return}u(s=>({...s,[e]:t}))},p=(e,t)=>{e.preventDefault(),c(null);let s=Array.from(e.dataTransfer.files);s.length>0&&x(t,s[0])},h=(e,t)=>{e.preventDefault(),c(t)},g=e=>{e.preventDefault(),c(null)},f=async e=>{let t=o[e];if(t)try{await s(e,t),u(t=>{let s={...t};return delete s[e],s})}catch(e){console.error("Upload error:",e)}},j=e=>{let s=t.find(t=>t.document_type===e);return s?.status||null},y=e=>{switch(e){case"approved":return r.jsx("span",{className:"text-green-500 text-xl",children:"✅"});case"pending":return r.jsx("span",{className:"text-yellow-500 text-xl",children:"⏳"});case"rejected":return r.jsx("span",{className:"text-red-500 text-xl",children:"❌"});default:return r.jsx("span",{className:"text-gray-400 text-xl",children:"\uD83D\uDCC4"})}},b=e=>{switch(e){case"approved":return"Approved";case"pending":return"Under Review";case"rejected":return"Rejected - Please reupload";default:return"Not uploaded"}},v=e=>{switch(e){case"approved":return"text-green-600";case"pending":return"text-yellow-600";case"rejected":return"text-red-600";default:return"text-gray-500"}};return(0,r.jsxs)("div",{className:"space-y-6",children:[e.map(e=>{let t=j(e.type),s=o[e.type],a="approved"===t,c=!a||"rejected"===t;return(0,r.jsxs)(d.Zb,{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[y(t),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"font-semibold text-gray-800",children:[e.name,e.required&&r.jsx("span",{className:"text-red-500 ml-1",children:"*"})]}),r.jsx("p",{className:"text-sm text-gray-600",children:e.description})]})]}),r.jsx("div",{className:"text-right",children:r.jsx("span",{className:`text-sm font-medium ${v(t)}`,children:b(t)})})]}),c&&(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx("div",{className:`border-2 border-dashed rounded-lg p-6 text-center transition-colors duration-200 ${l===e.type?"border-blue-500 bg-blue-50":s?"border-green-500 bg-green-50":"border-gray-300 hover:border-gray-400"}`,onDrop:t=>p(t,e.type),onDragOver:t=>h(t,e.type),onDragLeave:g,children:s?(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx("div",{className:"text-green-600 text-2xl",children:"\uD83D\uDCCE"}),r.jsx("p",{className:"font-medium text-gray-800",children:s.name}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:[(s.size/1024/1024).toFixed(2)," MB"]})]}):(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx("div",{className:"text-gray-400 text-4xl",children:"\uD83D\uDCC1"}),(0,r.jsxs)("p",{className:"text-gray-600",children:["Drag and drop your file here, or"," ",r.jsx("button",{type:"button",onClick:()=>m.current[e.type]?.click(),className:"text-blue-600 hover:text-blue-800 underline",children:"browse"})]}),r.jsx("p",{className:"text-xs text-gray-500",children:"Supported formats: JPG, PNG, PDF (max 10MB)"})]})}),r.jsx("input",{ref:t=>m.current[e.type]=t,type:"file",accept:".jpg,.jpeg,.png,.pdf",onChange:t=>{let s=t.target.files?.[0];s&&x(e.type,s)},className:"hidden"}),s&&(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[r.jsx("button",{type:"button",onClick:()=>{u(t=>{let s={...t};return delete s[e.type],s})},className:"text-sm text-gray-500 hover:text-gray-700",children:"Remove file"}),r.jsx(i.zx,{onClick:()=>f(e.type),disabled:n,variant:"primary",size:"sm",children:n?"Uploading...":"Upload Document"})]})]}),a&&r.jsx("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center text-green-800",children:[r.jsx("span",{className:"mr-2",children:"✅"}),r.jsx("span",{className:"font-medium",children:"Document approved and verified"})]})}),"rejected"===t&&r.jsx("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center text-red-800",children:[r.jsx("span",{className:"mr-2",children:"❌"}),r.jsx("span",{className:"font-medium",children:"Document rejected - please upload a new document"})]})})]},e.type)}),(0,r.jsxs)(d.Zb,{className:"p-6 bg-blue-50 border-blue-200",children:[r.jsx("h3",{className:"font-semibold text-blue-800 mb-3",children:"Document Upload Guidelines"}),(0,r.jsxs)("ul",{className:"text-sm text-blue-700 space-y-2",children:[(0,r.jsxs)("li",{className:"flex items-start",children:[r.jsx("span",{className:"mr-2",children:"•"}),r.jsx("span",{children:"Ensure all documents are clear, legible, and in color"})]}),(0,r.jsxs)("li",{className:"flex items-start",children:[r.jsx("span",{className:"mr-2",children:"•"}),r.jsx("span",{children:"Documents should not be older than 3 months (for proof of address)"})]}),(0,r.jsxs)("li",{className:"flex items-start",children:[r.jsx("span",{className:"mr-2",children:"•"}),r.jsx("span",{children:"All four corners of the document must be visible"})]}),(0,r.jsxs)("li",{className:"flex items-start",children:[r.jsx("span",{className:"mr-2",children:"•"}),r.jsx("span",{children:"For selfie with ID: hold your ID next to your face, both should be clearly visible"})]}),(0,r.jsxs)("li",{className:"flex items-start",children:[r.jsx("span",{className:"mr-2",children:"•"}),r.jsx("span",{children:"Maximum file size: 10MB per document"})]})]})]})]})}var o=s(7863);function u({steps:e,orientation:t="horizontal",className:s}){let a="horizontal"===t;return r.jsx("div",{className:(0,o.cn)("flex",a?"flex-row items-center":"flex-col",s),children:e.map((t,s)=>(0,r.jsxs)("div",{className:(0,o.cn)("flex items-center",a?"flex-row":"flex-col",s<e.length-1&&(a?"flex-1":"mb-8")),children:[(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("div",{className:(0,o.cn)("flex items-center justify-center w-8 h-8 rounded-full border-2 transition-all duration-200",t.completed?"bg-green-600 border-green-600 text-white":t.current?"bg-blue-600 border-blue-600 text-white":"bg-white border-gray-300 text-gray-500"),children:t.completed?r.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:r.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}):r.jsx("span",{className:"text-sm font-medium",children:s+1})}),(0,r.jsxs)("div",{className:(0,o.cn)("ml-3",a?"":"text-center"),children:[r.jsx("div",{className:(0,o.cn)("text-sm font-medium",t.completed||t.current?"text-gray-900":"text-gray-500"),children:t.title}),t.description&&r.jsx("div",{className:"text-xs text-gray-500 mt-1",children:t.description})]})]}),s<e.length-1&&r.jsx("div",{className:(0,o.cn)("transition-all duration-200",a?"flex-1 h-0.5 mx-4":"w-0.5 h-8 mx-auto mt-2",t.completed?"bg-green-600":"bg-gray-300")})]},t.id))})}function m({kycInfo:e,documents:t,requiredDocuments:s}){let a=e=>{let s=t.find(t=>t.document_type===e);return s?.status||"not_uploaded"},n=()=>{if(!e)return{percentage:0,status:"not_started",message:"KYC verification not started"};let t=s.filter(e=>e.required),r=t.filter(e=>"not_uploaded"!==a(e.type)),n=t.filter(e=>"approved"===a(e.type)),l=0,i="in_progress",d="KYC verification in progress";return"approved"===e.kyc_status?(l=100,i="completed",d="KYC verification completed successfully"):"rejected"===e.kyc_status?(l=25,i="rejected",d="KYC verification rejected - please resubmit documents"):"pending"===e.kyc_status?n.length===t.length?(l=90,d="All documents approved - final review in progress"):r.length===t.length?(l=75,d="All documents uploaded - under review"):(l=50,d="Some documents uploaded - please complete all uploads"):(l=Math.round(r.length/t.length*50),d=`${r.length} of ${t.length} documents uploaded`),{percentage:l,status:i,message:d}},l=n(),i=(n(),[{id:"personal_info",title:"Personal Information",description:"Basic information submitted",completed:!!e},{id:"document_upload",title:"Document Upload",description:"Required documents uploaded",completed:s.every(e=>!e.required||"not_uploaded"!==a(e.type))},{id:"review",title:"Document Review",description:"Documents under admin review",completed:e?.kyc_status==="pending"&&s.every(e=>!e.required||["approved","pending"].includes(a(e.type)))},{id:"approval",title:"Final Approval",description:"KYC verification completed",completed:e?.kyc_status==="approved"}]);return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("div",{className:"text-4xl mb-2",children:(e=>{switch(e){case"completed":return"✅";case"rejected":return"❌";case"in_progress":return"⏳";default:return"\uD83D\uDCCB"}})(l.status)}),r.jsx("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"KYC Verification Status"}),r.jsx("p",{className:`text-lg font-medium ${(e=>{switch(e){case"completed":return"text-green-600";case"rejected":return"text-red-600";case"in_progress":return"text-blue-600";default:return"text-gray-600"}})(l.status)}`,children:l.message})]}),r.jsx("div",{className:"bg-gray-200 rounded-full h-3 overflow-hidden",children:r.jsx("div",{className:`h-full transition-all duration-500 ease-out ${"completed"===l.status?"bg-green-500":"rejected"===l.status?"bg-red-500":"bg-blue-500"}`,style:{width:`${l.percentage}%`}})}),(0,r.jsxs)("div",{className:"text-center text-sm text-gray-600",children:[l.percentage,"% Complete"]}),r.jsx("div",{className:"mt-8",children:r.jsx(u,{steps:i,orientation:"vertical"})}),r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mt-8",children:s.map(e=>{let t=a(e.type);return(0,r.jsxs)("div",{className:`border rounded-lg p-4 ${(e=>{switch(e){case"approved":return"text-green-600 bg-green-50 border-green-200";case"pending":return"text-yellow-600 bg-yellow-50 border-yellow-200";case"rejected":return"text-red-600 bg-red-50 border-red-200";default:return"text-gray-600 bg-gray-50 border-gray-200"}})(t)}`,children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[r.jsx("span",{className:"text-2xl",children:(e=>{switch(e){case"approved":return"✅";case"pending":return"⏳";case"rejected":return"❌";default:return"\uD83D\uDCC4"}})(t)}),r.jsx("span",{className:"text-xs font-medium uppercase tracking-wide",children:(e=>{switch(e){case"approved":return"Approved";case"pending":return"Under Review";case"rejected":return"Rejected";default:return"Not Uploaded"}})(t)})]}),r.jsx("h3",{className:"font-medium text-gray-800 mb-1",children:e.name}),r.jsx("p",{className:"text-xs opacity-75",children:e.required?"Required":"Optional"})]},e.type)})}),e&&(0,r.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 mt-6",children:[r.jsx("h3",{className:"font-semibold text-gray-800 mb-3",children:"Verification Timeline"}),(0,r.jsxs)("div",{className:"space-y-2 text-sm text-gray-600",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[r.jsx("span",{children:"KYC Started:"}),r.jsx("span",{children:new Date(e.created_at).toLocaleDateString()})]}),e.verified_at&&(0,r.jsxs)("div",{className:"flex justify-between",children:[r.jsx("span",{children:"Verified:"}),r.jsx("span",{children:new Date(e.verified_at).toLocaleDateString()})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[r.jsx("span",{children:"Expected Completion:"}),r.jsx("span",{children:"approved"===e.kyc_status?"Completed":"1-3 business days"})]})]})]}),"rejected"===l.status&&(0,r.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[r.jsx("h3",{className:"font-semibold text-red-800 mb-2",children:"Action Required"}),r.jsx("p",{className:"text-red-700 text-sm mb-3",children:"Your KYC verification was rejected. Please review the feedback and resubmit the required documents."}),(0,r.jsxs)("div",{className:"text-xs text-red-600",children:[r.jsx("p",{children:"Common rejection reasons:"}),(0,r.jsxs)("ul",{className:"list-disc list-inside mt-1 space-y-1",children:[r.jsx("li",{children:"Document image is blurry or unclear"}),r.jsx("li",{children:"Document is expired or invalid"}),r.jsx("li",{children:"Information doesn't match profile data"}),r.jsx("li",{children:"Document corners are cut off"})]})]})]})]})}var x=s(6785);function p({documents:e,onDelete:t,canDelete:s}){let[n,l]=(0,a.useState)(null),[d,c]=(0,a.useState)(null),[o,u]=(0,a.useState)(!1),m=e=>({identity_document:"Identity Document",proof_of_address:"Proof of Address",selfie_with_id:"Selfie with ID",bank_statement:"Bank Statement",utility_bill:"Utility Bill",passport:"Passport",drivers_license:"Driver's License",national_id:"National ID"})[e]||e.replace("_"," ").replace(/\b\w/g,e=>e.toUpperCase()),p=e=>{switch(e){case"approved":return"✅";case"pending":return"⏳";case"rejected":return"❌";default:return"\uD83D\uDCC4"}},h=e=>{switch(e){case"approved":return"text-green-600 bg-green-50";case"pending":return"text-yellow-600 bg-yellow-50";case"rejected":return"text-red-600 bg-red-50";default:return"text-gray-600 bg-gray-50"}},g=e=>{switch(e){case"approved":return"Approved";case"pending":return"Under Review";case"rejected":return"Rejected";default:return"Unknown"}},f=e=>e.split(".").pop()?.toLowerCase()||"",j=e=>{switch(f(e)){case"pdf":return"\uD83D\uDCC4";case"jpg":case"jpeg":case"png":return"\uD83D\uDDBC️";default:return"\uD83D\uDCCE"}},y=async e=>{try{u(!0);let s=e.file_url.split("/").pop()||"";await t(e.id,s),c(null)}catch(e){console.error("Delete error:",e)}finally{u(!1)}},b=e=>{l(e)};return 0===e.length?(0,r.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[r.jsx("div",{className:"text-4xl mb-4",children:"\uD83D\uDCC4"}),r.jsx("p",{children:"No documents uploaded yet"})]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx("div",{className:"space-y-4",children:e.map(e=>r.jsx("div",{className:"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200",children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-4 flex-1",children:[r.jsx("div",{className:"text-2xl",children:j(e.file_name)}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[r.jsx("h3",{className:"font-medium text-gray-800 truncate",children:m(e.document_type)}),(0,r.jsxs)("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${h(e.status)}`,children:[p(e.status)," ",g(e.status)]})]}),r.jsx("p",{className:"text-sm text-gray-600 truncate mb-2",children:e.file_name}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 text-xs text-gray-500",children:[(0,r.jsxs)("span",{children:["Uploaded: ",new Date(e.uploaded_at).toLocaleDateString()]}),e.reviewed_at&&(0,r.jsxs)("span",{children:["Reviewed: ",new Date(e.reviewed_at).toLocaleDateString()]})]}),e.admin_notes&&(0,r.jsxs)("div",{className:"mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs",children:[r.jsx("strong",{children:"Admin Notes:"})," ",e.admin_notes]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:[r.jsx(i.zx,{variant:"outline",size:"sm",onClick:()=>b(e),children:"View"}),s&&"approved"!==e.status&&r.jsx(i.zx,{variant:"danger",size:"sm",onClick:()=>c(e.id),children:"Delete"})]})]})},e.id))}),r.jsx(x.u_,{isOpen:!!n,onClose:()=>l(null),size:"lg",children:n&&(0,r.jsxs)(r.Fragment,{children:[r.jsx(x.xB,{children:r.jsx(x.r6,{children:m(n.document_type)})}),r.jsx(x.fe,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"text-center",children:["pdf"===f(n.file_name)?(0,r.jsxs)("div",{className:"bg-gray-100 rounded-lg p-8",children:[r.jsx("div",{className:"text-4xl mb-4",children:"\uD83D\uDCC4"}),r.jsx("p",{className:"text-gray-600 mb-4",children:"PDF Document"}),r.jsx("a",{href:n.file_url,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200",children:"Open PDF"})]}):r.jsx("img",{src:n.file_url,alt:n.file_name,className:"max-w-full max-h-96 mx-auto rounded-lg shadow-lg",onError:e=>{let t=e.target;t.style.display="none",t.nextElementSibling?.classList.remove("hidden")}}),(0,r.jsxs)("div",{className:"hidden bg-gray-100 rounded-lg p-8",children:[r.jsx("div",{className:"text-4xl mb-4",children:"❌"}),r.jsx("p",{className:"text-gray-600",children:"Unable to load image"})]})]}),(0,r.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,r.jsxs)("div",{children:[r.jsx("span",{className:"font-medium text-gray-700",children:"File Name:"}),r.jsx("p",{className:"text-gray-600 break-all",children:n.file_name})]}),(0,r.jsxs)("div",{children:[r.jsx("span",{className:"font-medium text-gray-700",children:"Status:"}),(0,r.jsxs)("p",{className:h(n.status).split(" ")[0],children:[p(n.status)," ",g(n.status)]})]}),(0,r.jsxs)("div",{children:[r.jsx("span",{className:"font-medium text-gray-700",children:"Uploaded:"}),r.jsx("p",{className:"text-gray-600",children:new Date(n.uploaded_at).toLocaleString()})]}),n.reviewed_at&&(0,r.jsxs)("div",{children:[r.jsx("span",{className:"font-medium text-gray-700",children:"Reviewed:"}),r.jsx("p",{className:"text-gray-600",children:new Date(n.reviewed_at).toLocaleString()})]})]}),n.admin_notes&&(0,r.jsxs)("div",{className:"mt-4",children:[r.jsx("span",{className:"font-medium text-gray-700",children:"Admin Notes:"}),r.jsx("p",{className:"text-gray-600 mt-1",children:n.admin_notes})]})]})]})}),(0,r.jsxs)(x.mz,{children:[r.jsx(i.zx,{variant:"outline",onClick:()=>l(null),children:"Close"}),r.jsx("a",{href:n.file_url,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200",children:"Download"})]})]})}),(0,r.jsxs)(x.u_,{isOpen:!!d,onClose:()=>c(null),size:"sm",children:[r.jsx(x.xB,{children:r.jsx(x.r6,{children:"Confirm Delete"})}),r.jsx(x.fe,{children:r.jsx("p",{className:"text-gray-600",children:"Are you sure you want to delete this document? This action cannot be undone."})}),(0,r.jsxs)(x.mz,{children:[r.jsx(i.zx,{variant:"outline",onClick:()=>c(null),disabled:o,children:"Cancel"}),r.jsx(i.zx,{variant:"danger",onClick:()=>{let t=e.find(e=>e.id===d);t&&y(t)},disabled:o,children:o?"Deleting...":"Delete"})]})]})]})}var h=s(8998);function g(){let{user:e}=(0,n.a)(),{success:t,error:s}=(0,h.x_)(),[i,o]=(0,a.useState)(null),[u,x]=(0,a.useState)([]),[g,f]=(0,a.useState)(!0),[j,y]=(0,a.useState)(!1),b=async()=>{try{f(!0);let{data:t,error:s}=await l.OQ.from("kyc_information").select("*").eq("user_id",e?.telegram_profile?.id).single();if(s&&"PGRST116"!==s.code)throw s;o(t);let{data:r,error:a}=await l.OQ.from("kyc_documents").select("*").eq("user_id",e?.telegram_profile?.id).order("uploaded_at",{ascending:!1});if(a&&"PGRST116"!==a.code)throw a;x(r||[])}catch(e){console.error("Error loading KYC data:",e),s("Failed to load KYC information")}finally{f(!1)}},v=async(r,a)=>{try{y(!0);let s=a.name.split(".").pop(),n=`kyc_${e?.telegram_profile?.telegram_id}_${r}_${Date.now()}.${s}`,{data:i,error:d}=await l.OQ.storage.from("kyc-documents").upload(n,a);if(d)throw d;let{data:{publicUrl:c}}=l.OQ.storage.from("kyc-documents").getPublicUrl(n),{error:o}=await l.OQ.from("kyc_documents").insert({user_id:e?.telegram_profile?.id,document_type:r,file_name:a.name,file_url:c,file_path:i.path,file_size:a.size,mime_type:a.type,status:"pending",uploaded_by_telegram_id:e?.telegram_profile?.telegram_id});if(o)throw o;t("Document uploaded successfully"),await b()}catch(e){console.error("Error uploading document:",e),s("Failed to upload document")}finally{y(!1)}},N=async(e,r)=>{try{let{error:s}=await l.OQ.storage.from("kyc-documents").remove([r]);s&&console.warn("Storage deletion error:",s);let{error:a}=await l.OQ.from("kyc_documents").delete().eq("id",e);if(a)throw a;t("Document deleted successfully"),await b()}catch(e){console.error("Error deleting document:",e),s("Failed to delete document")}},w=[{type:"identity_document",name:"Identity Document",description:"National ID, Passport, or Driver's License",required:!0},{type:"proof_of_address",name:"Proof of Address",description:"Utility bill, bank statement, or lease agreement (not older than 3 months)",required:!0},{type:"selfie_with_id",name:"Selfie with ID",description:"Clear photo of yourself holding your ID document",required:!0}];return g?r.jsx("div",{className:"container mx-auto px-4 py-8",children:(0,r.jsxs)("div",{className:"animate-pulse space-y-6",children:[r.jsx("div",{className:"h-8 bg-gray-200 rounded w-1/3"}),r.jsx("div",{className:"h-32 bg-gray-200 rounded"}),r.jsx("div",{className:"h-64 bg-gray-200 rounded"})]})}):(!i||i.kyc_status,(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-800 mb-2",children:"KYC Verification"}),r.jsx("p",{className:"text-gray-600",children:"Complete your Know Your Customer verification to access all platform features"})]}),r.jsx(d.Zb,{className:"mb-8",children:r.jsx("div",{className:"p-6",children:r.jsx(m,{kycInfo:i,documents:u,requiredDocuments:w})})}),(!i||"approved"!==i.kyc_status)&&r.jsx(d.Zb,{className:"mb-8",children:(0,r.jsxs)("div",{className:"p-6",children:[r.jsx("h2",{className:"text-xl font-semibold text-gray-800 mb-4",children:"Upload Documents"}),r.jsx(c,{requiredDocuments:w,existingDocuments:u,onUpload:v,uploading:j})]})}),u.length>0&&r.jsx(d.Zb,{children:(0,r.jsxs)("div",{className:"p-6",children:[r.jsx("h2",{className:"text-xl font-semibold text-gray-800 mb-4",children:"Uploaded Documents"}),r.jsx(p,{documents:u,onDelete:N,canDelete:i?.kyc_status!=="approved"})]})}),r.jsx(d.Zb,{className:"mt-8",children:(0,r.jsxs)("div",{className:"p-6",children:[r.jsx("h2",{className:"text-xl font-semibold text-gray-800 mb-4",children:"Need Help?"}),(0,r.jsxs)("div",{className:"space-y-4 text-sm text-gray-600",children:[(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"Document Requirements:"})," All documents must be clear, legible, and in color. Accepted formats: JPG, PNG, PDF (max 10MB per file)."]}),(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"Processing Time:"})," KYC verification typically takes 1-3 business days. You'll receive a notification once your documents are reviewed."]}),(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"Support:"})," If you have questions or need assistance, contact our support team at"," ",r.jsx("a",{href:"https://t.me/AureusAllianceBot",target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:underline",children:"@AureusAllianceBot"})]})]})]})})]}))}},6785:(e,t,s)=>{"use strict";s.d(t,{fe:()=>o,mz:()=>u,r6:()=>c,u_:()=>i,xB:()=>d});var r=s(326),a=s(7577),n=s(962),l=s(7863);function i({isOpen:e,onClose:t,children:s,size:i="md",closeOnOverlayClick:d=!0,closeOnEscape:c=!0,showCloseButton:o=!0,className:u}){let m=(0,a.useRef)(null);if(!e)return null;let x=r.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50 animate-in fade-in duration-200",onClick:e=>{d&&e.target===e.currentTarget&&t()},children:(0,r.jsxs)("div",{ref:m,className:(0,l.cn)("bg-white rounded-lg shadow-xl w-full animate-in zoom-in-95 duration-200",{sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl",full:"max-w-full mx-4"}[i],u),tabIndex:-1,role:"dialog","aria-modal":"true",children:[o&&r.jsx("button",{onClick:t,className:"absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors z-10","aria-label":"Close modal",children:r.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),s]})});return(0,n.createPortal)(x,document.body)}function d({children:e,className:t}){return r.jsx("div",{className:(0,l.cn)("px-6 py-4 border-b border-gray-200",t),children:e})}function c({children:e,className:t}){return r.jsx("h2",{className:(0,l.cn)("text-xl font-semibold text-gray-800",t),children:e})}function o({children:e,className:t}){return r.jsx("div",{className:(0,l.cn)("px-6 py-4",t),children:e})}function u({children:e,className:t}){return r.jsx("div",{className:(0,l.cn)("px-6 py-4 border-t border-gray-200 flex justify-end space-x-3",t),children:e})}},8998:(e,t,s)=>{"use strict";s.d(t,{x_:()=>l}),s(326);var r=s(7577);let a=(0,r.createContext)(void 0);function n(){let e=(0,r.useContext)(a);if(void 0===e)throw Error("useToast must be used within a ToastProvider");return e}function l(){let{addToast:e}=n();return{success:(t,s)=>{e({type:"success",message:t,...s})},error:(t,s)=>{e({type:"error",message:t,...s})},warning:(t,s)=>{e({type:"warning",message:t,...s})},info:(t,s)=>{e({type:"info",message:t,...s})}}}},4276:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(8570).createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\app\dashboard\kyc\page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[276,471,529,325,585,973],()=>s(7116));module.exports=r})();