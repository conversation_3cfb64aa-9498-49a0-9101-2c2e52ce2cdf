(()=>{var e={};e.id=128,e.ids=[128],e.modules={399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},8893:e=>{"use strict";e.exports=require("buffer")},4770:e=>{"use strict";e.exports=require("crypto")},7702:e=>{"use strict";e.exports=require("events")},2615:e=>{"use strict";e.exports=require("http")},8791:e=>{"use strict";e.exports=require("https")},8216:e=>{"use strict";e.exports=require("net")},8621:e=>{"use strict";e.exports=require("punycode")},6162:e=>{"use strict";e.exports=require("stream")},2452:e=>{"use strict";e.exports=require("tls")},7360:e=>{"use strict";e.exports=require("url")},1568:e=>{"use strict";e.exports=require("zlib")},8359:()=>{},3739:()=>{},287:(e,r,t)=>{"use strict";t.r(r),t.d(r,{originalPathname:()=>_,patchFetch:()=>R,requestAsyncStorage:()=>g,routeModule:()=>h,serverHooks:()=>f,staticGenerationAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>m});var a=t(9303),i=t(8716),o=t(670),u=t(7070),n=t(2438),c=t(4770);let l=process.env.SUPABASE_SERVICE_ROLE_KEY,p=process.env.TELEGRAM_BOT_TOKEN,d=(0,n.eI)("https://fgubaqoftdeefcakejwu.supabase.co",l,{auth:{autoRefreshToken:!1,persistSession:!1}});async function m(e){try{let r=new URL(e.url),t=Object.fromEntries(r.searchParams.entries());if(!function(e){if(!e)return!1;let{hash:r,...t}=e,s=Object.keys(t).sort().map(e=>`${e}=${t[e]}`).join("\n"),a=(0,c.createHash)("sha256").update(p).digest();return(0,c.createHmac)("sha256",a).update(s).digest("hex")===r}(t))return u.NextResponse.redirect(new URL("/login?error=Invalid+authentication",e.url));let s={id:t.id.toString(),username:t.username||"",firstName:t.first_name||"",lastName:t.last_name||"",photoUrl:t.photo_url||"",authDate:t.auth_date},{data:a,error:i}=await d.from("telegram_users").select("*").eq("telegram_id",s.id).single();if(i&&"PGRST116"!==i.code)return console.error("Error finding user:",i),u.NextResponse.redirect(new URL("/login?error=Database+error",e.url));let{data:o,error:n}=await d.auth.admin.createUser({email:`telegram_${s.id}@aureus.africa`,password:`telegram_${s.id}_${Date.now()}`,email_confirm:!0,user_metadata:{telegram_id:s.id,telegram_username:s.username,telegram_first_name:s.firstName,telegram_last_name:s.lastName}});if(n)return console.error("Error creating auth user:",n),u.NextResponse.redirect(new URL("/login?error=Auth+error",e.url));let{data:l,error:m}=await d.auth.admin.createSession({user_id:o.user.id,expires_in:604800});if(m)return console.error("Error creating session:",m),u.NextResponse.redirect(new URL("/login?error=Session+error",e.url));let h=u.NextResponse.redirect(new URL("/dashboard",e.url));return h.cookies.set("sb-access-token",l.access_token,{path:"/",maxAge:604800,sameSite:"lax",secure:!0}),h.cookies.set("sb-refresh-token",l.refresh_token,{path:"/",maxAge:604800,sameSite:"lax",secure:!0}),h}catch(r){return console.error("Telegram auth error:",r),u.NextResponse.redirect(new URL("/login?error=Server+error",e.url))}}let h=new a.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/auth/telegram-callback/route",pathname:"/api/auth/telegram-callback",filename:"route",bundlePath:"app/api/auth/telegram-callback/route"},resolvedPagePath:"C:\\xampp\\htdocs\\aureus_africa\\app\\api\\auth\\telegram-callback\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:g,staticGenerationAsyncStorage:x,serverHooks:f}=h,_="/api/auth/telegram-callback/route";function R(){return(0,o.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:x})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[276,169],()=>t(287));module.exports=s})();