(()=>{var e={};e.id=526,e.ids=[526],e.modules={399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},8893:e=>{"use strict";e.exports=require("buffer")},4770:e=>{"use strict";e.exports=require("crypto")},7702:e=>{"use strict";e.exports=require("events")},2615:e=>{"use strict";e.exports=require("http")},8791:e=>{"use strict";e.exports=require("https")},8216:e=>{"use strict";e.exports=require("net")},8621:e=>{"use strict";e.exports=require("punycode")},6162:e=>{"use strict";e.exports=require("stream")},2452:e=>{"use strict";e.exports=require("tls")},7360:e=>{"use strict";e.exports=require("url")},1568:e=>{"use strict";e.exports=require("zlib")},8359:()=>{},3739:()=>{},1265:(e,t,r)=>{"use strict";r.r(t),r.d(t,{originalPathname:()=>_,patchFetch:()=>g,requestAsyncStorage:()=>l,routeModule:()=>p,serverHooks:()=>f,staticGenerationAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{GET:()=>d,POST:()=>c});var o=r(9303),i=r(8716),a=r(670),n=r(7070),u=r(1066);async function d(e){try{let t=e.cookies.get("sb-access-token")?.value;if(!t)return n.NextResponse.json({error:"No access token provided"},{status:401});let{data:{user:r},error:s}=await u.OQ.auth.getUser(t);if(s||!r)return n.NextResponse.json({error:"Invalid or expired token"},{status:401});let o=r.user_metadata?.telegram_id;if(!o)return n.NextResponse.json({error:"No Telegram ID found"},{status:400});let{data:i,error:a}=await u.OQ.from("telegram_users").select("*").eq("telegram_id",o).single();if(a||!i)return n.NextResponse.json({error:"Telegram user profile not found"},{status:404});let{data:d,error:c}=await u.OQ.from("kyc_information").select("*").eq("user_id",i.id).single(),{data:p,error:l}=await u.OQ.from("kyc_documents").select("*").eq("user_id",i.id).order("uploaded_at",{ascending:!1});if(l&&"PGRST116"!==l.code)return n.NextResponse.json({error:"Failed to get KYC documents"},{status:500});let m=["identity_document","proof_of_address","selfie_with_id"],f=p?.map(e=>e.document_type)||[],_=p?.filter(e=>"approved"===e.status).map(e=>e.document_type)||[],g={hasKycInfo:!!d,documentsUploaded:m.filter(e=>f.includes(e)).length,documentsApproved:m.filter(e=>_.includes(e)).length,totalRequired:m.length,isComplete:d?.kyc_status==="approved",status:d?.kyc_status||"incomplete",completionPercentage:d?.kyc_status==="approved"?100:Math.round(f.length/m.length*75+(d?25:0))};return n.NextResponse.json({success:!0,kycInfo:d||null,documents:p||[],completionStatus:g,requiredDocuments:[{type:"identity_document",name:"Identity Document",description:"National ID, Passport, or Driver's License",required:!0,uploaded:f.includes("identity_document"),approved:_.includes("identity_document")},{type:"proof_of_address",name:"Proof of Address",description:"Utility bill, bank statement, or lease agreement (not older than 3 months)",required:!0,uploaded:f.includes("proof_of_address"),approved:_.includes("proof_of_address")},{type:"selfie_with_id",name:"Selfie with ID",description:"Clear photo of yourself holding your ID document",required:!0,uploaded:f.includes("selfie_with_id"),approved:_.includes("selfie_with_id")}]})}catch(e){return console.error("Get KYC status error:",e),n.NextResponse.json({error:"Failed to get KYC status"},{status:500})}}async function c(e){try{let t=e.cookies.get("sb-access-token")?.value;if(!t)return n.NextResponse.json({error:"No access token provided"},{status:401});let{data:{user:r},error:s}=await u.OQ.auth.getUser(t);if(s||!r)return n.NextResponse.json({error:"Invalid or expired token"},{status:401});let{action:o,documentId:i,adminNotes:a}=await e.json(),d=r.user_metadata?.telegram_id;if(!d)return n.NextResponse.json({error:"No Telegram ID found"},{status:400});let{data:c,error:p}=await u.OQ.from("telegram_users").select("*").eq("telegram_id",d).single();if(p||!c)return n.NextResponse.json({error:"Telegram user profile not found"},{status:404});switch(o){case"delete_document":let{error:l}=await u.OQ.from("kyc_documents").delete().eq("id",i).eq("user_id",c.id);if(l)throw l;return n.NextResponse.json({success:!0,message:"Document deleted successfully"});case"resubmit_kyc":let{error:m}=await u.OQ.from("kyc_information").update({kyc_status:"pending",updated_at:new Date().toISOString()}).eq("user_id",c.id);if(m)throw m;let{error:f}=await u.OQ.from("kyc_documents").update({status:"pending",admin_notes:null,reviewed_at:null}).eq("user_id",c.id);return f&&console.warn("Error resetting document statuses:",f),n.NextResponse.json({success:!0,message:"KYC resubmitted for review"});default:return n.NextResponse.json({error:"Invalid action"},{status:400})}}catch(e){return console.error("KYC action error:",e),n.NextResponse.json({error:"Failed to process KYC action"},{status:500})}}let p=new o.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/kyc/status/route",pathname:"/api/kyc/status",filename:"route",bundlePath:"app/api/kyc/status/route"},resolvedPagePath:"C:\\xampp\\htdocs\\aureus_africa\\app\\api\\kyc\\status\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:l,staticGenerationAsyncStorage:m,serverHooks:f}=p,_="/api/kyc/status/route";function g(){return(0,a.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:m})}},1066:(e,t,r)=>{"use strict";r.d(t,{OQ:()=>a});var s=r(2438);let o=(void 0).VITE_SUPABASE_URL||"https://fgubaqoftdeefcakejwu.supabase.co",i=(void 0).VITE_SUPABASE_ANON_KEY;if(!i)throw Error("Missing VITE_SUPABASE_ANON_KEY environment variable");let a=(0,s.eI)(o,i,{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[276,169],()=>r(1265));module.exports=s})();