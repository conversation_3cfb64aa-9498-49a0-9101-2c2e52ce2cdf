(()=>{var e={};e.id=420,e.ids=[420],e.modules={399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},8893:e=>{"use strict";e.exports=require("buffer")},4770:e=>{"use strict";e.exports=require("crypto")},7702:e=>{"use strict";e.exports=require("events")},2615:e=>{"use strict";e.exports=require("http")},8791:e=>{"use strict";e.exports=require("https")},8216:e=>{"use strict";e.exports=require("net")},8621:e=>{"use strict";e.exports=require("punycode")},6162:e=>{"use strict";e.exports=require("stream")},2452:e=>{"use strict";e.exports=require("tls")},7360:e=>{"use strict";e.exports=require("url")},1568:e=>{"use strict";e.exports=require("zlib")},8359:()=>{},3739:()=>{},7258:(e,r,t)=>{"use strict";t.r(r),t.d(r,{originalPathname:()=>m,patchFetch:()=>g,requestAsyncStorage:()=>l,routeModule:()=>d,serverHooks:()=>x,staticGenerationAsyncStorage:()=>f});var s={};t.r(s),t.d(s,{GET:()=>p,PUT:()=>c});var o=t(9303),a=t(8716),u=t(670),i=t(7070),n=t(1066);async function p(e){try{let r=e.cookies.get("sb-access-token")?.value;if(!r)return i.NextResponse.json({error:"No access token provided"},{status:401});let{data:{user:t},error:s}=await n.OQ.auth.getUser(r);if(s||!t)return i.NextResponse.json({error:"Invalid or expired token"},{status:401});let o=t.user_metadata?.telegram_id,a=null;if(o){let{data:e,error:r}=await n.OQ.from("telegram_users").select("*").eq("telegram_id",o).single();!r&&e&&(a=e)}let u=!1,p=!1,c=!1;if(a){let{data:e}=await n.OQ.from("terms_acceptance").select("*").eq("user_id",a.id).single();u=!!e,p=!!a.country;let{data:r}=await n.OQ.from("kyc_information").select("verification_status").eq("user_id",a.id).single();c=r?.verification_status==="approved"}return i.NextResponse.json({user:{...t,telegram_profile:a,hasAcceptedTerms:u,hasSelectedCountry:p,hasCompletedKYC:c}})}catch(e){return console.error("Get user error:",e),i.NextResponse.json({error:"Failed to get user data"},{status:500})}}async function c(e){try{let r=e.cookies.get("sb-access-token")?.value;if(!r)return i.NextResponse.json({error:"No access token provided"},{status:401});let{data:{user:t},error:s}=await n.OQ.auth.getUser(r);if(s||!t)return i.NextResponse.json({error:"Invalid or expired token"},{status:401});let{country:o,phone:a,address:u}=await e.json(),p=t.user_metadata?.telegram_id;if(!p)return i.NextResponse.json({error:"No Telegram ID found"},{status:400});let{data:c,error:d}=await n.OQ.from("telegram_users").update({country:o,phone:a,address:u,updated_at:new Date().toISOString()}).eq("telegram_id",p).select().single();if(d)return console.error("Update user error:",d),i.NextResponse.json({error:"Failed to update user profile"},{status:500});return i.NextResponse.json({success:!0,user:c})}catch(e){return console.error("Update user error:",e),i.NextResponse.json({error:"Failed to update user profile"},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/auth/user/route",pathname:"/api/auth/user",filename:"route",bundlePath:"app/api/auth/user/route"},resolvedPagePath:"C:\\xampp\\htdocs\\aureus_africa\\app\\api\\auth\\user\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:l,staticGenerationAsyncStorage:f,serverHooks:x}=d,m="/api/auth/user/route";function g(){return(0,u.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:f})}},1066:(e,r,t)=>{"use strict";t.d(r,{OQ:()=>u});var s=t(2438);let o=(void 0).VITE_SUPABASE_URL||"https://fgubaqoftdeefcakejwu.supabase.co",a=(void 0).VITE_SUPABASE_ANON_KEY;if(!a)throw Error("Missing VITE_SUPABASE_ANON_KEY environment variable");let u=(0,s.eI)(o,a,{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[276,169],()=>t(7258));module.exports=s})();