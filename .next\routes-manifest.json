{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [], "staticRoutes": [{"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/dashboard/kyc", "regex": "^/dashboard/kyc(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/kyc(?:/)?$"}, {"page": "/dashboard/payments", "regex": "^/dashboard/payments(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/payments(?:/)?$"}, {"page": "/dashboard/purchase", "regex": "^/dashboard/purchase(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/purchase(?:/)?$"}, {"page": "/dashboard/purchase/usdt", "regex": "^/dashboard/purchase/usdt(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/purchase/usdt(?:/)?$"}, {"page": "/dashboard/purchase/zar", "regex": "^/dashboard/purchase/zar(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/purchase/zar(?:/)?$"}, {"page": "/dashboard/referrals", "regex": "^/dashboard/referrals(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/referrals(?:/)?$"}, {"page": "/dashboard/referrals/convert", "regex": "^/dashboard/referrals/convert(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/referrals/convert(?:/)?$"}, {"page": "/dashboard/referrals/withdraw", "regex": "^/dashboard/referrals/withdraw(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/referrals/withdraw(?:/)?$"}, {"page": "/dashboard/shares", "regex": "^/dashboard/shares(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/shares(?:/)?$"}, {"page": "/onboarding/complete", "regex": "^/onboarding/complete(?:/)?$", "routeKeys": {}, "namedRegex": "^/onboarding/complete(?:/)?$"}, {"page": "/onboarding/country", "regex": "^/onboarding/country(?:/)?$", "routeKeys": {}, "namedRegex": "^/onboarding/country(?:/)?$"}, {"page": "/onboarding/kyc", "regex": "^/onboarding/kyc(?:/)?$", "routeKeys": {}, "namedRegex": "^/onboarding/kyc(?:/)?$"}, {"page": "/onboarding/terms", "regex": "^/onboarding/terms(?:/)?$", "routeKeys": {}, "namedRegex": "^/onboarding/terms(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}