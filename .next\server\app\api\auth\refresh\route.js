(()=>{var e={};e.id=575,e.ids=[575],e.modules={399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},8893:e=>{"use strict";e.exports=require("buffer")},4770:e=>{"use strict";e.exports=require("crypto")},7702:e=>{"use strict";e.exports=require("events")},2615:e=>{"use strict";e.exports=require("http")},8791:e=>{"use strict";e.exports=require("https")},8216:e=>{"use strict";e.exports=require("net")},8621:e=>{"use strict";e.exports=require("punycode")},6162:e=>{"use strict";e.exports=require("stream")},2452:e=>{"use strict";e.exports=require("tls")},7360:e=>{"use strict";e.exports=require("url")},1568:e=>{"use strict";e.exports=require("zlib")},8359:()=>{},3739:()=>{},420:(e,r,s)=>{"use strict";s.r(r),s.d(r,{originalPathname:()=>d,patchFetch:()=>l,requestAsyncStorage:()=>h,routeModule:()=>c,serverHooks:()=>f,staticGenerationAsyncStorage:()=>x});var t={};s.r(t),s.d(t,{POST:()=>p});var o=s(9303),i=s(8716),a=s(670),u=s(7070),n=s(1066);async function p(e){try{let r=e.cookies.get("sb-refresh-token")?.value;if(!r)return u.NextResponse.json({error:"No refresh token provided"},{status:401});let{data:{session:s},error:t}=await n.OQ.auth.refreshSession({refresh_token:r});if(t||!s){let e=u.NextResponse.json({error:"Failed to refresh session"},{status:401});return e.cookies.set("sb-access-token","",{path:"/",expires:new Date(0),sameSite:"lax",secure:!0}),e.cookies.set("sb-refresh-token","",{path:"/",expires:new Date(0),sameSite:"lax",secure:!0}),e}let o=u.NextResponse.json({success:!0,user:s.user});return o.cookies.set("sb-access-token",s.access_token,{path:"/",maxAge:604800,sameSite:"lax",secure:!0}),s.refresh_token&&o.cookies.set("sb-refresh-token",s.refresh_token,{path:"/",maxAge:2592e3,sameSite:"lax",secure:!0}),o}catch(e){return console.error("Session refresh error:",e),u.NextResponse.json({error:"Session refresh failed"},{status:500})}}let c=new o.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/auth/refresh/route",pathname:"/api/auth/refresh",filename:"route",bundlePath:"app/api/auth/refresh/route"},resolvedPagePath:"C:\\xampp\\htdocs\\aureus_africa\\app\\api\\auth\\refresh\\route.ts",nextConfigOutput:"",userland:t}),{requestAsyncStorage:h,staticGenerationAsyncStorage:x,serverHooks:f}=c,d="/api/auth/refresh/route";function l(){return(0,a.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:x})}},1066:(e,r,s)=>{"use strict";s.d(r,{OQ:()=>a});var t=s(2438);let o=(void 0).VITE_SUPABASE_URL||"https://fgubaqoftdeefcakejwu.supabase.co",i=(void 0).VITE_SUPABASE_ANON_KEY;if(!i)throw Error("Missing VITE_SUPABASE_ANON_KEY environment variable");let a=(0,t.eI)(o,i,{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}})}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[276,169],()=>s(420));module.exports=t})();