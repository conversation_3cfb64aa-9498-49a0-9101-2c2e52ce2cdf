import React from 'react';
import { createRoot } from 'react-dom/client';
import App from './App';
import ErrorBoundary from './components/ErrorBoundary';
import { SiteContentProvider } from './contexts/SiteContentContext';
import { AuthProvider } from './contexts/AuthContext';
import './index.css';

// FIXED: React 19 compatible root creation
const rootElement = document.getElementById('root');
if (!rootElement) {
  throw new Error("Could not find root element to mount to");
}

// Create root only once - this was the bug!
const root = createRoot(rootElement);

root.render(
  <React.StrictMode>
    <ErrorBoundary>
      <AuthProvider>
        <SiteContentProvider>
          <App />
        </SiteContentProvider>
      </AuthProvider>
    </ErrorBoundary>
  </React.StrictMode>
);