(()=>{var e={};e.id=427,e.ids=[427],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},8893:e=>{"use strict";e.exports=require("buffer")},4770:e=>{"use strict";e.exports=require("crypto")},7702:e=>{"use strict";e.exports=require("events")},2615:e=>{"use strict";e.exports=require("http")},8791:e=>{"use strict";e.exports=require("https")},8216:e=>{"use strict";e.exports=require("net")},8621:e=>{"use strict";e.exports=require("punycode")},6162:e=>{"use strict";e.exports=require("stream")},2452:e=>{"use strict";e.exports=require("tls")},7360:e=>{"use strict";e.exports=require("url")},1568:e=>{"use strict";e.exports=require("zlib")},9924:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d}),s(5544),s(3321),s(5866);var t=s(3191),a=s(8716),n=s(7922),l=s.n(n),i=s(5231),o={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(r,o);let d=["",{children:["dashboard",{children:["purchase",{children:["usdt",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,5544)),"C:\\xampp\\htdocs\\aureus_africa\\app\\dashboard\\purchase\\usdt\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,3321)),"C:\\xampp\\htdocs\\aureus_africa\\app\\dashboard\\layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(s.t.bind(s,5866,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\xampp\\htdocs\\aureus_africa\\app\\dashboard\\purchase\\usdt\\page.tsx"],u="/dashboard/purchase/usdt/page",m={require:s,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/dashboard/purchase/usdt/page",pathname:"/dashboard/purchase/usdt",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},2766:(e,r,s)=>{Promise.resolve().then(s.bind(s,8767))},8438:(e,r,s)=>{Promise.resolve().then(s.bind(s,2347))},8767:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>l});var t=s(326),a=s(9240),n=s(668);function l({children:e}){let{user:r,signOut:s}=(0,a.a)(),l=async()=>{await s()};return t.jsx(n.Z,{children:(0,t.jsxs)("div",{className:"dashboard-layout min-h-screen bg-gray-50",children:[t.jsx("header",{className:"bg-gradient-to-r from-gray-900 via-blue-900 to-purple-900 text-white shadow-lg",children:t.jsx("div",{className:"container mx-auto px-4 py-4",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[t.jsx("h1",{className:"text-2xl font-bold text-yellow-400",children:"Aureus Alliance"}),t.jsx("span",{className:"text-sm text-gray-300",children:"Dashboard"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[r?.telegram_profile&&(0,t.jsxs)("div",{className:"text-sm",children:[t.jsx("span",{className:"text-gray-300",children:"Welcome, "}),t.jsx("span",{className:"text-yellow-400 font-medium",children:r.telegram_profile.first_name})]}),t.jsx("button",{onClick:l,className:"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 text-sm font-medium",children:"Logout"})]})]})})}),t.jsx("main",{className:"container mx-auto px-4 py-8",children:e})]})})}},2347:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>u});var t=s(326),a=s(7577),n=s(5047),l=s(9240),i=s(1579);s(286);var o=s(668);let d=[{code:"ETH",name:"Ethereum (ETH)",fee:"High fees, secure"},{code:"BSC",name:"Binance Smart Chain (BSC)",fee:"Low fees, fast"},{code:"POLYGON",name:"Polygon (MATIC)",fee:"Very low fees, fast"},{code:"TRON",name:"TRON (TRX)",fee:"Low fees, popular in Asia"}],c={ETH:"******************************************",BSC:"******************************************",POLYGON:"******************************************",TRON:"TLa2f6VPqDgRE67v1736s7bJ8Ray5wYjU7"};function u(){let{user:e}=(0,l.a)(),r=(0,n.useRouter)();(0,n.useSearchParams)();let[s,u]=(0,a.useState)(""),[m,p]=(0,a.useState)(""),[h,x]=(0,a.useState)(""),[b,g]=(0,a.useState)(""),[f,y]=(0,a.useState)(""),[j,w]=(0,a.useState)(null),[v,N]=(0,a.useState)(!1),[S,C]=(0,a.useState)(null),_=async r=>{try{let s=r.name.split(".").pop(),t=`${e?.telegram_profile?.telegram_id}_${Date.now()}.${s}`,{data:a,error:n}=await i.OQ.storage.from("payment-proofs").upload(t,r);if(n)return console.error("Upload error:",n),null;let{data:{publicUrl:l}}=i.OQ.storage.from("payment-proofs").getPublicUrl(t);return l}catch(e){return console.error("Upload error:",e),null}},P=async t=>{if(t.preventDefault(),!e?.telegram_profile?.id){C("User profile not found");return}if(!b||!h){C("Please fill in all required fields");return}N(!0),C(null);try{let t=null;if(j&&!(t=await _(j)))throw Error("Failed to upload screenshot");let{error:a}=await i.OQ.from("crypto_payment_transactions").insert({user_id:e.telegram_profile.id,amount:parseFloat(s),currency:"USDT",network:h,sender_wallet:b,receiver_wallet:c[h],transaction_hash:f||null,screenshot_url:t,status:"pending"});if(a)throw Error(a.message);r.push("/dashboard/payments?status=submitted")}catch(e){console.error("Payment submission error:",e),C("Failed to submit payment. Please try again.")}finally{N(!1)}},q=h?c[h]:"";return t.jsx(o.Z,{requireTerms:!0,requireCountry:!0,children:t.jsx("div",{className:"max-w-2xl mx-auto",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,t.jsxs)("div",{className:"mb-6",children:[t.jsx("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:"USDT Payment"}),t.jsx("p",{className:"text-gray-600",children:"Complete your share purchase using USDT cryptocurrency"})]}),(0,t.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4 mb-6 border border-blue-100",children:[t.jsx("h2",{className:"font-semibold text-blue-800 mb-2",children:"Purchase Summary"}),(0,t.jsxs)("div",{className:"space-y-1 text-sm text-blue-700",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{children:"Amount:"}),(0,t.jsxs)("span",{children:["$",parseFloat(s||"0").toFixed(2)," USD"]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{children:"Shares:"}),t.jsx("span",{children:parseInt(m||"0").toLocaleString()})]})]})]}),(0,t.jsxs)("form",{onSubmit:P,className:"space-y-6",children:[(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-gray-700 font-medium mb-2",children:"USDT Network *"}),(0,t.jsxs)("select",{value:h,onChange:e=>x(e.target.value),required:!0,className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500",children:[t.jsx("option",{value:"",children:"Select network"}),d.map(e=>(0,t.jsxs)("option",{value:e.code,children:[e.name," - ",e.fee]},e.code))]})]}),h&&(0,t.jsxs)("div",{className:"bg-yellow-50 rounded-lg p-4 border border-yellow-200",children:[t.jsx("h3",{className:"font-medium text-yellow-800 mb-2",children:"Send USDT to this address:"}),t.jsx("div",{className:"bg-white p-3 rounded border font-mono text-sm break-all",children:q}),(0,t.jsxs)("p",{className:"text-yellow-700 text-sm mt-2",children:["⚠️ Only send USDT on ",d.find(e=>e.code===h)?.name," network to this address"]})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-gray-700 font-medium mb-2",children:"Your Wallet Address *"}),t.jsx("input",{type:"text",value:b,onChange:e=>g(e.target.value),placeholder:"Enter the wallet address you're sending from",required:!0,className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"}),t.jsx("p",{className:"text-gray-500 text-sm mt-1",children:"This helps us verify the transaction"})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-gray-700 font-medium mb-2",children:"Transaction Hash (Optional)"}),t.jsx("input",{type:"text",value:f,onChange:e=>y(e.target.value),placeholder:"Enter transaction hash if available",className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"}),t.jsx("p",{className:"text-gray-500 text-sm mt-1",children:"You can add this later if not available now"})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-gray-700 font-medium mb-2",children:"Payment Screenshot (Optional)"}),t.jsx("input",{type:"file",accept:"image/*",onChange:e=>{let r=e.target.files?.[0];if(r){if(!r.type.startsWith("image/")){C("Please upload an image file");return}if(r.size>5242880){C("File size must be less than 5MB");return}w(r),C(null)}},className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"}),t.jsx("p",{className:"text-gray-500 text-sm mt-1",children:"Upload a screenshot of your transaction for faster processing"})]}),S&&t.jsx("div",{className:"p-3 bg-red-100 text-red-700 rounded-md text-sm",children:S}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("button",{type:"button",onClick:()=>r.push("/dashboard/purchase"),className:"px-6 py-2 bg-gray-300 hover:bg-gray-400 text-gray-800 rounded-lg transition-colors duration-200",children:"Back"}),t.jsx("button",{type:"submit",disabled:v||!h||!b,className:`px-6 py-2 rounded-lg transition-colors duration-200 ${!v&&h&&b?"bg-blue-600 hover:bg-blue-700 text-white":"bg-blue-300 text-white cursor-not-allowed"}`,children:v?"Submitting...":"Submit Payment"})]})]}),(0,t.jsxs)("div",{className:"mt-8 bg-gray-50 rounded-lg p-4",children:[t.jsx("h3",{className:"font-medium text-gray-800 mb-2",children:"Payment Instructions"}),(0,t.jsxs)("ol",{className:"text-sm text-gray-600 space-y-1 list-decimal list-inside",children:[t.jsx("li",{children:"Select the USDT network you want to use"}),(0,t.jsxs)("li",{children:["Send exactly $",s," worth of USDT to the displayed address"]}),t.jsx("li",{children:"Enter your wallet address and transaction details"}),t.jsx("li",{children:"Submit this form for admin review"}),t.jsx("li",{children:"You'll be notified once your payment is approved"})]})]})]})})})}},286:(e,r,s)=>{"use strict";s.d(r,{B_:()=>d,f6:()=>o,fj:()=>i,od:()=>t,pD:()=>a});let t=[{code:"ZA",name:"South Africa",region:"africa"},{code:"SZ",name:"Eswatini",region:"africa"},{code:"NA",name:"Namibia",region:"africa"},{code:"BW",name:"Botswana",region:"africa"},{code:"ZW",name:"Zimbabwe",region:"africa"},{code:"MZ",name:"Mozambique",region:"africa"},{code:"LS",name:"Lesotho",region:"africa"},{code:"US",name:"United States",region:"western"},{code:"GB",name:"United Kingdom",region:"western"},{code:"CA",name:"Canada",region:"western"},{code:"AU",name:"Australia",region:"western"},{code:"NZ",name:"New Zealand",region:"western"},{code:"SG",name:"Singapore",region:"asia"},{code:"AE",name:"United Arab Emirates",region:"asia"},{code:"OTHER",name:"Other Country",region:"other"}],a=e=>{let r=t.find(r=>r.code===e);return r?r.name:"Unknown Country"},n=e=>["ZA","SZ","NA"].includes(e),l=e=>!0,i=e=>{let r=[];return l(e)&&r.push("USDT"),n(e)&&r.push("ZAR"),r},o=e=>["US","GB","CA"].includes(e),d=e=>n(e)?"You can pay using ZAR bank transfer or USDT cryptocurrency.":"You can pay using USDT cryptocurrency on multiple networks."},3321:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(8570).createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\app\dashboard\layout.tsx#default`)},5544:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(8570).createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\app\dashboard\purchase\usdt\page.tsx#default`)}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[276,471,529,585],()=>s(9924));module.exports=t})();