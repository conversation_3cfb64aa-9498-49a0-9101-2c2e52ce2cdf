import { useState, useEffect } from 'react';

interface GoldPriceData {
  price: number;
  lastUpdated: string;
  isLoading: boolean;
  error: string | null;
}

export const useGoldPrice = () => {
  const [goldPrice, setGoldPrice] = useState<GoldPriceData>({
    price: 100000, // Default fallback price
    lastUpdated: '',
    isLoading: false,
    error: null,
  });

  const fetchGoldPrice = async () => {
    setGoldPrice(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // For development - use simulated/fixed gold price to avoid network issues
      console.log('💰 Using simulated gold price for development');

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Use current approximate gold price (as of 2024)
      const currentGoldPricePerOunce = 2650.00; // USD per troy ounce

      const data = {
        price: currentGoldPricePerOunce,
        timestamp: Date.now(),
        source: 'simulated_dev_data'
      };
      
      // Convert from USD per troy ounce to USD per kg
      // 1 troy ounce = 31.1035 grams, so 1 kg = 32.15 troy ounces
      const pricePerKg = Math.round(data.price * 32.15);

      setGoldPrice({
        price: pricePerKg,
        lastUpdated: new Date().toLocaleString(),
        isLoading: false,
        error: null,
      });
    } catch (error) {
      console.warn('💰 Using fallback gold price for development:', error);
      // Use fallback price for development
      const fallbackPricePerKg = Math.round(2650 * 32.15); // ~85,200 USD per kg
      setGoldPrice({
        price: fallbackPricePerKg,
        lastUpdated: new Date().toLocaleString(),
        isLoading: false,
        error: null, // Don't show error in development
      });
    }
  };

  useEffect(() => {
    // Fetch gold price on component mount
    fetchGoldPrice();
    
    // Set up interval to fetch every 5 minutes
    const interval = setInterval(fetchGoldPrice, 5 * 60 * 1000);
    
    return () => clearInterval(interval);
  }, []);

  return {
    ...goldPrice,
    refetch: fetchGoldPrice,
  };
};
