(()=>{var e={};e.id=784,e.ids=[784],e.modules={399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},8893:e=>{"use strict";e.exports=require("buffer")},4770:e=>{"use strict";e.exports=require("crypto")},7702:e=>{"use strict";e.exports=require("events")},2615:e=>{"use strict";e.exports=require("http")},8791:e=>{"use strict";e.exports=require("https")},8216:e=>{"use strict";e.exports=require("net")},8621:e=>{"use strict";e.exports=require("punycode")},6162:e=>{"use strict";e.exports=require("stream")},2452:e=>{"use strict";e.exports=require("tls")},7360:e=>{"use strict";e.exports=require("url")},1568:e=>{"use strict";e.exports=require("zlib")},8359:()=>{},3739:()=>{},8925:(e,t,r)=>{"use strict";r.r(t),r.d(t,{originalPathname:()=>g,patchFetch:()=>f,requestAsyncStorage:()=>l,routeModule:()=>p,serverHooks:()=>_,staticGenerationAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{GET:()=>c,POST:()=>d});var o=r(9303),a=r(8716),n=r(670),i=r(7070),u=r(1066);async function c(e){try{let t=e.cookies.get("sb-access-token")?.value;if(!t)return i.NextResponse.json({error:"No access token provided"},{status:401});let{data:{user:r},error:s}=await u.OQ.auth.getUser(t);if(s||!r)return i.NextResponse.json({error:"Invalid or expired token"},{status:401});let o=r.user_metadata?.telegram_id;if(!o)return i.NextResponse.json({error:"No Telegram ID found"},{status:400});let{data:a,error:n}=await u.OQ.from("telegram_users").select("*").eq("telegram_id",o).single();if(n||!a)return i.NextResponse.json({error:"Telegram user profile not found"},{status:404});let{data:c}=await u.OQ.from("terms_acceptance").select("*").eq("user_id",a.id).single(),d=!!a.country,{data:p}=await u.OQ.from("kyc_information").select("*").eq("user_id",a.id).single(),l=p?.kyc_status||"incomplete",m=function(e,t,r,s){let o=[e,t,r],a=o.filter(Boolean).length,n=o.length,i="complete",u=!1;return e?t?r?(i="complete",u=!0):i="kyc":i="country":i="terms",{hasAcceptedTerms:e,hasSelectedCountry:t,hasCompletedKYC:r,kycStatus:s,completionPercentage:Math.round(a/n*100),nextStep:i,canAccessDashboard:u}}(!!c,d,!!p,l);return i.NextResponse.json({success:!0,status:m,details:{termsAcceptance:c?{accepted_at:c.accepted_at,version:c.version}:null,country:a.country,kycInfo:p?{status:p.kyc_status,submitted_at:p.created_at,verified_at:p.verified_at}:null}})}catch(e){return console.error("Get onboarding status error:",e),i.NextResponse.json({error:"Failed to get onboarding status"},{status:500})}}async function d(e){try{let t;let r=e.cookies.get("sb-access-token")?.value;if(!r)return i.NextResponse.json({error:"No access token provided"},{status:401});let{data:{user:s},error:o}=await u.OQ.auth.getUser(r);if(o||!s)return i.NextResponse.json({error:"Invalid or expired token"},{status:401});let{step:a,data:n}=await e.json(),c=s.user_metadata?.telegram_id;if(!c)return i.NextResponse.json({error:"No Telegram ID found"},{status:400});let{data:d,error:p}=await u.OQ.from("telegram_users").select("*").eq("telegram_id",c).single();if(p||!d)return i.NextResponse.json({error:"Telegram user profile not found"},{status:404});switch(a){case"terms":let{error:l}=await u.OQ.from("terms_acceptance").insert({user_id:d.id,terms_type:"general",version:n.version||"1.0",accepted_at:new Date().toISOString()});if(l)throw Error(l.message);t={step:"terms",completed:!0};break;case"country":let{error:m}=await u.OQ.from("telegram_users").update({country:n.country,updated_at:new Date().toISOString()}).eq("id",d.id);if(m)throw Error(m.message);t={step:"country",completed:!0,country:n.country};break;case"kyc":let{error:_}=await u.OQ.from("kyc_information").insert({user_id:d.id,first_name:n.firstName,last_name:n.lastName,id_type:n.idType,id_number_encrypted:n.idNumber,id_number_hash:n.idNumber,phone_number:n.phoneNumber,email_address:n.emailAddress,street_address:n.address,city:n.city,postal_code:n.postalCode,country_code:d.country||"ZA",country_name:d.country||"South Africa",data_consent_given:n.acceptedPrivacy,privacy_policy_accepted:n.acceptedPrivacy,kyc_status:"pending",created_by_telegram_id:d.telegram_id});if(_)throw Error(_.message);t={step:"kyc",completed:!0,status:"pending"};break;default:return i.NextResponse.json({error:"Invalid onboarding step"},{status:400})}return i.NextResponse.json({success:!0,result:t})}catch(e){return console.error("Update onboarding step error:",e),i.NextResponse.json({error:"Failed to update onboarding step"},{status:500})}}let p=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/onboarding/status/route",pathname:"/api/onboarding/status",filename:"route",bundlePath:"app/api/onboarding/status/route"},resolvedPagePath:"C:\\xampp\\htdocs\\aureus_africa\\app\\api\\onboarding\\status\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:l,staticGenerationAsyncStorage:m,serverHooks:_}=p,g="/api/onboarding/status/route";function f(){return(0,n.patchFetch)({serverHooks:_,staticGenerationAsyncStorage:m})}},1066:(e,t,r)=>{"use strict";r.d(t,{OQ:()=>n});var s=r(2438);let o=(void 0).VITE_SUPABASE_URL||"https://fgubaqoftdeefcakejwu.supabase.co",a=(void 0).VITE_SUPABASE_ANON_KEY;if(!a)throw Error("Missing VITE_SUPABASE_ANON_KEY environment variable");let n=(0,s.eI)(o,a,{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[276,169],()=>r(8925));module.exports=s})();