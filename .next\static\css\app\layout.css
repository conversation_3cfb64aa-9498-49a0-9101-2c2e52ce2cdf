/*!**********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./aureus-design-system.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************/
/* ===== PROFESSIONAL AUREUS DESIGN SYSTEM ===== */

:root {
  /* Professional Color Palette */
  --primary-gold: #d4af37;
  --secondary-gold: #b8860b;
  --accent-blue: #1e40af;
  --text-primary: #ffffff;
  --text-secondary: #e5e7eb;
  --text-muted: #9ca3af;
  --background-dark: #000000;
  --background-card: rgba(0, 0, 0, 0.4);
  --border-subtle: rgba(255, 255, 255, 0.15);

  /* Responsive Professional Spacing - Mobile First */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 0.75rem;
  --spacing-lg: 1rem;
  --spacing-xl: 1.25rem;
  --spacing-2xl: 1.5rem;
  --spacing-3xl: 2rem;

  /* Typography */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;

  /* Tight Layout */
  --container-max-width: 1400px;
  --section-padding: var(--spacing-xl);
  --card-padding: var(--spacing-md);

  /* Responsive Breakpoints */
  --breakpoint-mobile: 320px;
  --breakpoint-mobile-lg: 480px;
  --breakpoint-tablet: 768px;
  --breakpoint-tablet-lg: 1024px;
  --breakpoint-desktop: 1200px;
  --breakpoint-desktop-lg: 1400px;
  --breakpoint-desktop-xl: 1920px;
}

/* ===== PROFESSIONAL BASE STYLES ===== */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: var(--background-dark);
  color: var(--text-primary);
  line-height: 1.6;
  font-size: clamp(1rem, 1.5vw, 1.125rem);
  letter-spacing: 2px;
}

/* Mobile-first typography scaling */
@media (min-width: 480px) {
  body {
    font-size: clamp(1.125rem, 1.75vw, 1.25rem);
    letter-spacing: 2px;
  }
}

@media (min-width: 768px) {
  body {
    line-height: 1.7;
    font-size: clamp(1.125rem, 2vw, 1.25rem);
    letter-spacing: 2px;
  }

  :root {
    --spacing-xl: 1.5rem;
    --spacing-2xl: 2rem;
    --spacing-3xl: 2.5rem;
  }
}

@media (min-width: 1024px) {
  :root {
    --spacing-xl: 2rem;
    --spacing-2xl: 2.5rem;
    --spacing-3xl: 3rem;
  }
}

@media (min-width: 1200px) {
  :root {
    --spacing-xl: 2.5rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
  }
}

/* ===== PROFESSIONAL LAYOUT SYSTEM ===== */

/* Clean Container System - Mobile First */
.container {
  width: 100%;
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--spacing-sm); /* Start with smaller mobile padding */
}

@media (min-width: 480px) {
  .container {
    padding: 0 var(--spacing-md);
  }
}

@media (min-width: 768px) {
  .container {
    padding: 0 var(--spacing-lg);
  }
}

@media (min-width: 1200px) {
  .container {
    padding: 0 var(--spacing-xl);
  }
}

/* Additional Responsive Container Classes */
.container-sm {
  width: 100%;
  max-width: 640px;
  margin: 0 auto;
  padding: 0 var(--spacing-sm);
}

.container-md {
  width: 100%;
  max-width: 768px;
  margin: 0 auto;
  padding: 0 var(--spacing-sm);
}

.container-lg {
  width: 100%;
  max-width: 1024px;
  margin: 0 auto;
  padding: 0 var(--spacing-sm);
}

.container-xl {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--spacing-sm);
}

.container-fluid {
  width: 100%;
  padding: 0 var(--spacing-sm);
}

@media (min-width: 480px) {
  .container-sm,
  .container-md,
  .container-lg,
  .container-xl,
  .container-fluid {
    padding: 0 var(--spacing-md);
  }
}

@media (min-width: 768px) {
  .container-sm,
  .container-md,
  .container-lg,
  .container-xl,
  .container-fluid {
    padding: 0 var(--spacing-lg);
  }
}

@media (min-width: 1200px) {
  .container-sm,
  .container-md,
  .container-lg,
  .container-xl,
  .container-fluid {
    padding: 0 var(--spacing-xl);
  }
}

/* Mobile-First Section Spacing */
.section {
  padding: var(--spacing-md) 0; /* Smaller mobile spacing */
}

.section-hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  padding: var(--spacing-lg) 0; /* Smaller mobile hero padding */
}

.section-compact {
  padding: var(--spacing-sm) 0;
}

@media (min-width: 768px) {
  .section {
    padding: var(--spacing-lg) 0;
  }

  .section-hero {
    padding: var(--spacing-xl) 0;
  }

  .section-compact {
    padding: var(--spacing-md) 0;
  }
}

@media (min-width: 1024px) {
  .section {
    padding: var(--spacing-xl) 0;
  }

  .section-hero {
    padding: var(--spacing-2xl) 0;
  }
}

/* Tight Grid System */
.grid {
  display: grid;
  gap: var(--spacing-md);
  width: 100%;
}

.grid-1 { grid-template-columns: 1fr; }
.grid-2 { grid-template-columns: repeat(2, 1fr); }
.grid-3 { grid-template-columns: repeat(3, 1fr); }
.grid-4 { grid-template-columns: repeat(4, 1fr); }

/* Mobile-first responsive grid system */
@media (max-width: 767px) {
  .grid-2,
  .grid-3,
  .grid-4 {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .grid-3,
  .grid-4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Professional Flexbox Utilities */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-sm { gap: var(--spacing-sm); }
.gap-md { gap: var(--spacing-md); }
.gap-lg { gap: var(--spacing-lg); }

/* ===== ENHANCED PROFESSIONAL TYPOGRAPHY SYSTEM ===== */

.heading-hero {
    font-size: clamp(1.0rem, 6vw, 3rem);
}

.heading-xl {
  font-size: clamp(2rem, 6vw, 3rem);
  font-weight: 600;
  line-height: 1.2;
  letter-spacing: 2px;
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.heading-lg {
  font-size: clamp(1.75rem, 5vw, 2.5rem);
  font-weight: 600;
  line-height: 1.3;
  letter-spacing: 2px;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.heading-md {
  font-size: clamp(1.5rem, 4vw, 2rem);
  font-weight: 500;
  line-height: 1.4;
  letter-spacing: 2px;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.heading-sm {
  font-size: clamp(1.25rem, 3vw, 1.75rem);
  font-weight: 500;
  line-height: 1.4;
  letter-spacing: 2px;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.heading-xs {
  font-size: clamp(1.125rem, 2.5vw, 1.5rem);
  font-weight: 500;
  line-height: 1.5;
  letter-spacing: 2px;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.text-lg {
  font-size: clamp(1.25rem, 2.5vw, 1.5rem);
  line-height: 1.6;
  letter-spacing: 2px;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}

.text-base {
  font-size: clamp(1.125rem, 2vw, 1.25rem);
  line-height: 1.6;
  letter-spacing: 2px;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}

.text-sm {
  font-size: clamp(1rem, 1.5vw, 1.125rem);
  line-height: 1.5;
  letter-spacing: 2px;
  color: var(--text-muted);
  margin-bottom: var(--spacing-xs);
}

/* Additional Typography Sizes for Enhanced Hierarchy */
.text-xl {
  font-size: clamp(1.5rem, 3vw, 1.75rem);
  line-height: 1.5;
  letter-spacing: 2px;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}

.text-xs {
  font-size: clamp(0.875rem, 1.25vw, 1rem);
  line-height: 1.4;
  letter-spacing: 2px;
  color: var(--text-muted);
  margin-bottom: var(--spacing-xs);
}

/* Professional Typography Variants */
.text-description {
  font-size: clamp(1.125rem, 2vw, 1.25rem);
  line-height: 1.6;
  letter-spacing: 2px;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}

.text-label {
  font-size: clamp(1rem, 1.5vw, 1.125rem);
  line-height: 1.4;
  letter-spacing: 2px;
  color: var(--text-muted);
  font-weight: 500;
  text-transform: uppercase;
}

.text-feature {
  font-size: clamp(1rem, 1.75vw, 1.125rem);
  line-height: 1.5;
  letter-spacing: 2px;
  color: var(--text-secondary);
}

/* Professional Color Classes */
.text-gold {
  color: var(--primary-gold);
}

.text-blue {
  color: var(--accent-blue);
}

/* ===== PROFESSIONAL COMPONENTS ===== */

/* Responsive Card System - Mobile First */
.card {
  background: var(--background-card);
  border-radius: 0.5rem;
  padding: var(--spacing-sm);
  transition: all 0.3s ease;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.card:hover {
  background: rgba(0, 0, 0, 0.6);
}

.card-compact {
  padding: var(--spacing-xs);
}

.card-hero {
  padding: var(--spacing-md);
}

/* Card responsive padding */
@media (min-width: 480px) {
  .card {
    padding: var(--spacing-md);
  }

  .card-compact {
    padding: var(--spacing-sm);
  }

  .card-hero {
    padding: var(--spacing-lg);
  }
}

@media (min-width: 768px) {
  .card {
    padding: var(--card-padding);
  }

  .card-hero {
    padding: var(--spacing-xl);
  }
}

@media (min-width: 1024px) {
  .card-hero {
    padding: var(--spacing-2xl);
  }
}

/* Card layout utilities */
.card-grid {
  display: grid;
  gap: var(--spacing-md);
  grid-template-columns: 1fr;
}

@media (min-width: 768px) {
  .card-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
  }
}

@media (min-width: 1024px) {
  .card-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-xl);
  }
}

.card-flex {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

@media (min-width: 768px) {
  .card-flex {
    flex-direction: row;
    gap: var(--spacing-md);
  }
}

/* Glass Card System for Admin */
.glass-card {
  background: rgba(0, 0, 0, 0.85);
  -webkit-backdrop-filter: blur(20px);
          backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 1.5rem;
  padding: 2.5rem;
  transition: all 0.3s ease;
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.glass-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.5), transparent);
}

.glass-card:hover {
  background: rgba(0, 0, 0, 0.9);
  border-color: rgba(212, 175, 55, 0.3);
  box-shadow:
    0 25px 80px rgba(0, 0, 0, 0.6),
    0 0 0 1px rgba(212, 175, 55, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

.glass-card-strong {
  background: rgba(0, 0, 0, 0.9);
  -webkit-backdrop-filter: blur(25px);
          backdrop-filter: blur(25px);
  border: 1px solid rgba(212, 175, 55, 0.4);
  box-shadow:
    0 30px 100px rgba(0, 0, 0, 0.7),
    0 0 0 1px rgba(212, 175, 55, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Enhanced Professional Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: 0.5rem;
  font-weight: 500;
  font-size: clamp(1rem, 1.5vw, 1.125rem);
  letter-spacing: 2px;
  text-decoration: none;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  white-space: nowrap;
  min-height: 44px; /* Touch-friendly minimum */
  min-width: 44px;
  box-sizing: border-box;
  text-transform: uppercase;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-gold), var(--secondary-gold));
  color: var(--background-dark);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(212, 175, 55, 0.3);
}

.btn-secondary {
  background: transparent;
  color: var(--text-primary);
  border: 1px solid var(--border-subtle);
}

.btn-secondary:hover {
  background: var(--background-card);
  border-color: var(--primary-gold);
}

.btn-outline {
  background: transparent;
  color: var(--primary-gold);
  border: 1px solid var(--primary-gold);
}

.btn-outline:hover {
  background: var(--primary-gold);
  color: var(--background-dark);
  border-color: var(--primary-gold);
}

/* Button size variants */
.btn-sm {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: clamp(0.875rem, 1.25vw, 1rem);
  letter-spacing: 2px;
  min-height: 36px;
}

.btn-lg {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: clamp(1.125rem, 1.75vw, 1.25rem);
  letter-spacing: 2px;
  min-height: 52px;
}

/* Mobile responsive button adjustments */
@media (max-width: 767px) {
  .btn {
    min-height: 48px; /* Larger touch targets on mobile */
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: clamp(1rem, 2vw, 1.125rem);
    letter-spacing: 2px;
  }

  .btn-sm {
    min-height: 40px;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: clamp(0.875rem, 1.5vw, 1rem);
    letter-spacing: 2px;
  }

  .btn-lg {
    min-height: 56px;
    padding: var(--spacing-lg) var(--spacing-xl);
    font-size: clamp(1.125rem, 2.5vw, 1.25rem);
    letter-spacing: 2px;
  }
}

/* Button layout utilities */
.btn-block {
  width: 100%;
  display: flex;
}

.btn-group {
  display: flex;
  gap: var(--spacing-sm);
}

@media (max-width: 767px) {
  .btn-group {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .btn-group .btn {
    width: 100%;
  }
}

/* Professional Spacing Utilities */
.mb-xs { margin-bottom: var(--spacing-xs); }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }

.mt-xs { margin-top: var(--spacing-xs); }
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }

.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }

/* Override Tailwind Conflicts - Clean Approach */
.container,
.max-w-7xl,
.max-w-6xl,
.max-w-5xl,
.max-w-4xl,
.max-w-3xl {
  width: 100% !important;
  max-width: var(--container-max-width) !important;
  margin: 0 auto !important;
  padding: 0 var(--spacing-md) !important;
}

@media (min-width: 768px) {
  .container,
  .max-w-7xl,
  .max-w-6xl,
  .max-w-5xl,
  .max-w-4xl,
  .max-w-3xl {
    padding: 0 var(--spacing-lg) !important;
  }
}

/* Professional Gradient Text Utilities */
.text-gradient-gold {
  background: linear-gradient(135deg, var(--primary-gold), var(--secondary-gold));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
}

.text-gradient-cyber {
  background: linear-gradient(135deg, var(--accent-blue), var(--primary-gold));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
}

/* Clean Section Spacing */
.py-24, .py-32, .py-20, .py-16 {
  padding-top: var(--section-padding) !important;
  padding-bottom: var(--section-padding) !important;
}

/* Remove Excessive Margins */
.mb-20, .mb-16, .mb-12, .mb-8, .mb-6, .mb-4 {
  margin-bottom: var(--spacing-sm) !important;
}

/* Clean Gaps */
.gap-12, .gap-16, .gap-8, .gap-6, .gap-4 {
  gap: var(--spacing-md) !important;
}

/* Remove Overlapping */
.-mt-8, .-mt-12, .-mt-16 {
  margin-top: 0 !important;
}

/* ===== FOOTER STYLES ===== */
.footer-container {
  background: rgba(0, 0, 0, 0.95);
  padding: var(--spacing-2xl) 0;
  border-top: 1px solid var(--border-subtle);
}

.footer-content {
  text-align: center;
}

.footer-logo {
  font-size: clamp(2rem, 4vw, 2.5rem);
  font-weight: 700;
  color: var(--primary-gold);
  margin-bottom: var(--spacing-lg);
  letter-spacing: 3px;
}

.footer-nav {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.footer-legal-nav {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
}

.footer-legal-nav a {
  color: var(--text-muted);
  font-size: var(--font-size-sm);
  text-decoration: none;
  transition: color 0.3s ease;
  letter-spacing: 1px;
}

.footer-legal-nav a:hover {
  color: var(--primary-gold);
}

.footer-cta {
  margin-bottom: var(--spacing-xl);
}

.footer-legal {
  color: var(--text-muted);
  font-size: var(--font-size-sm);
  line-height: 1.6;
}

.footer-legal p {
  margin-bottom: var(--spacing-sm);
}

/* Mobile responsive footer */
@media (max-width: 767px) {
  .footer-nav {
    gap: var(--spacing-md);
  }

  .footer-legal-nav {
    gap: var(--spacing-sm);
    flex-direction: column;
    align-items: center;
  }
}

/* ===== LEGAL SECTIONS ===== */
.legal-section {
  min-height: 100vh;
  padding: var(--spacing-3xl) 0;
  background: var(--background-dark);
  color: var(--text-primary);
}

.legal-content {
  max-width: 800px;
  margin: 0 auto;
  padding: var(--spacing-xl);
}

.back-button {
  background: transparent;
  border: 1px solid var(--primary-gold);
  color: var(--primary-gold);
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: 4px;
  cursor: pointer;
  margin-bottom: var(--spacing-xl);
  transition: all 0.3s ease;
  font-size: var(--font-size-sm);
}

.back-button:hover {
  background: var(--primary-gold);
  color: var(--background-dark);
}

.legal-text {
  line-height: 1.6;
}

.legal-text h2 {
  color: var(--primary-gold);
  font-size: var(--font-size-xl);
  margin: var(--spacing-xl) 0 var(--spacing-md) 0;
  font-weight: 600;
}

.legal-text p {
  margin-bottom: var(--spacing-md);
  color: var(--text-secondary);
}

.legal-links {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  flex-wrap: wrap;
}

.legal-links a {
  color: var(--primary-gold);
  text-decoration: none;
  font-size: var(--font-size-sm);
  transition: color 0.3s ease;
}

.legal-links a:hover {
  color: var(--secondary-gold);
  text-decoration: underline;
}

@media (max-width: 768px) {
  .legal-content {
    padding: var(--spacing-md);
  }

  .legal-links {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
}

/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[14].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"app\\layout.tsx","import":"Inter","arguments":[{"subsets":["latin"]}],"variableName":"inter"} ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic-ext */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/55c55f0601d81cf3-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/26a46d62cd723877-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/97e0cb1ae144a2a9-s.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/581909926a08bbc8-s.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* vietnamese */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/df0a9ae256c0569c-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/8e9860b6e62d6359-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/e4af272ccee01ff0-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: '__Inter_Fallback_e8ce0c';src: local("Arial");ascent-override: 90.49%;descent-override: 22.56%;line-gap-override: 0.00%;size-adjust: 107.06%
}.__className_e8ce0c {font-family: '__Inter_e8ce0c', '__Inter_Fallback_e8ce0c';font-style: normal
}

/*!*******************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./index.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************/
/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */
@layer properties;
:root {
  --primary-gold: #d4af37;
  --secondary-gold: #b8860b;
  --accent-blue: #1e40af;
  --text-primary: #ffffff;
  --text-secondary: #e5e7eb;
  --text-muted: #9ca3af;
  --background-dark: #000000;
  --background-card: rgba(0, 0, 0, 0.4);
  --border-subtle: rgba(255, 255, 255, 0.15);
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 0.75rem;
  --spacing-lg: 1rem;
  --spacing-xl: 1.25rem;
  --spacing-2xl: 1.5rem;
  --spacing-3xl: 2rem;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;
  --container-max-width: 1400px;
  --section-padding: var(--spacing-xl);
  --card-padding: var(--spacing-md);
  --breakpoint-mobile: 320px;
  --breakpoint-mobile-lg: 480px;
  --breakpoint-tablet: 768px;
  --breakpoint-tablet-lg: 1024px;
  --breakpoint-desktop: 1200px;
  --breakpoint-desktop-lg: 1400px;
  --breakpoint-desktop-xl: 1920px;
}
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: var(--background-dark);
  color: var(--text-primary);
  line-height: 1.6;
  font-size: clamp(1rem, 1.5vw, 1.125rem);
  letter-spacing: 2px;
}
@media (min-width: 480px) {
  body {
    font-size: clamp(1.125rem, 1.75vw, 1.25rem);
    letter-spacing: 2px;
  }
}
@media (min-width: 768px) {
  body {
    line-height: 1.7;
    font-size: clamp(1.125rem, 2vw, 1.25rem);
    letter-spacing: 2px;
  }
  :root {
    --spacing-xl: 1.5rem;
    --spacing-2xl: 2rem;
    --spacing-3xl: 2.5rem;
  }
}
@media (min-width: 1024px) {
  :root {
    --spacing-xl: 2rem;
    --spacing-2xl: 2.5rem;
    --spacing-3xl: 3rem;
  }
}
@media (min-width: 1200px) {
  :root {
    --spacing-xl: 2.5rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
  }
}
.container {
  width: 100%;
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--spacing-sm);
}
@media (min-width: 480px) {
  .container {
    padding: 0 var(--spacing-md);
  }
}
@media (min-width: 768px) {
  .container {
    padding: 0 var(--spacing-lg);
  }
}
@media (min-width: 1200px) {
  .container {
    padding: 0 var(--spacing-xl);
  }
}
.container-sm {
  width: 100%;
  max-width: 640px;
  margin: 0 auto;
  padding: 0 var(--spacing-sm);
}
.container-md {
  width: 100%;
  max-width: 768px;
  margin: 0 auto;
  padding: 0 var(--spacing-sm);
}
.container-lg {
  width: 100%;
  max-width: 1024px;
  margin: 0 auto;
  padding: 0 var(--spacing-sm);
}
.container-xl {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--spacing-sm);
}
.container-fluid {
  width: 100%;
  padding: 0 var(--spacing-sm);
}
@media (min-width: 480px) {
  .container-sm, .container-md, .container-lg, .container-xl, .container-fluid {
    padding: 0 var(--spacing-md);
  }
}
@media (min-width: 768px) {
  .container-sm, .container-md, .container-lg, .container-xl, .container-fluid {
    padding: 0 var(--spacing-lg);
  }
}
@media (min-width: 1200px) {
  .container-sm, .container-md, .container-lg, .container-xl, .container-fluid {
    padding: 0 var(--spacing-xl);
  }
}
.section {
  padding: var(--spacing-md) 0;
}
.section-hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  padding: var(--spacing-lg) 0;
}
.section-compact {
  padding: var(--spacing-sm) 0;
}
@media (min-width: 768px) {
  .section {
    padding: var(--spacing-lg) 0;
  }
  .section-hero {
    padding: var(--spacing-xl) 0;
  }
  .section-compact {
    padding: var(--spacing-md) 0;
  }
}
@media (min-width: 1024px) {
  .section {
    padding: var(--spacing-xl) 0;
  }
  .section-hero {
    padding: var(--spacing-2xl) 0;
  }
}
.grid {
  display: grid;
  gap: var(--spacing-md);
  width: 100%;
}
.grid-1 {
  grid-template-columns: 1fr;
}
.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}
.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}
.grid-4 {
  grid-template-columns: repeat(4, 1fr);
}
@media (max-width: 767px) {
  .grid-2, .grid-3, .grid-4 {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }
}
@media (min-width: 768px) and (max-width: 1023px) {
  .grid-3, .grid-4 {
    grid-template-columns: repeat(2, 1fr);
  }
}
.flex {
  display: flex;
}
.flex-col {
  flex-direction: column;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-sm {
  gap: var(--spacing-sm);
}
.gap-md {
  gap: var(--spacing-md);
}
.gap-lg {
  gap: var(--spacing-lg);
}
.heading-hero {
  font-size: clamp(1.0rem, 6vw, 3rem);
}
.heading-xl {
  font-size: clamp(2rem, 6vw, 3rem);
  font-weight: 600;
  line-height: 1.2;
  letter-spacing: 2px;
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}
.heading-lg {
  font-size: clamp(1.75rem, 5vw, 2.5rem);
  font-weight: 600;
  line-height: 1.3;
  letter-spacing: 2px;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}
.heading-md {
  font-size: clamp(1.5rem, 4vw, 2rem);
  font-weight: 500;
  line-height: 1.4;
  letter-spacing: 2px;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}
.heading-sm {
  font-size: clamp(1.25rem, 3vw, 1.75rem);
  font-weight: 500;
  line-height: 1.4;
  letter-spacing: 2px;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}
.heading-xs {
  font-size: clamp(1.125rem, 2.5vw, 1.5rem);
  font-weight: 500;
  line-height: 1.5;
  letter-spacing: 2px;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}
.text-lg {
  font-size: clamp(1.25rem, 2.5vw, 1.5rem);
  line-height: 1.6;
  letter-spacing: 2px;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}
.text-base {
  font-size: clamp(1.125rem, 2vw, 1.25rem);
  line-height: 1.6;
  letter-spacing: 2px;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}
.text-sm {
  font-size: clamp(1rem, 1.5vw, 1.125rem);
  line-height: 1.5;
  letter-spacing: 2px;
  color: var(--text-muted);
  margin-bottom: var(--spacing-xs);
}
.text-xl {
  font-size: clamp(1.5rem, 3vw, 1.75rem);
  line-height: 1.5;
  letter-spacing: 2px;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}
.text-xs {
  font-size: clamp(0.875rem, 1.25vw, 1rem);
  line-height: 1.4;
  letter-spacing: 2px;
  color: var(--text-muted);
  margin-bottom: var(--spacing-xs);
}
.text-description {
  font-size: clamp(1.125rem, 2vw, 1.25rem);
  line-height: 1.6;
  letter-spacing: 2px;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}
.text-label {
  font-size: clamp(1rem, 1.5vw, 1.125rem);
  line-height: 1.4;
  letter-spacing: 2px;
  color: var(--text-muted);
  font-weight: 500;
  text-transform: uppercase;
}
.text-feature {
  font-size: clamp(1rem, 1.75vw, 1.125rem);
  line-height: 1.5;
  letter-spacing: 2px;
  color: var(--text-secondary);
}
.text-gold {
  color: var(--primary-gold);
}
.text-blue {
  color: var(--accent-blue);
}
.card {
  background: var(--background-card);
  border-radius: 0.5rem;
  padding: var(--spacing-sm);
  transition: all 0.3s ease;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}
.card:hover {
  background: rgba(0, 0, 0, 0.6);
}
.card-compact {
  padding: var(--spacing-xs);
}
.card-hero {
  padding: var(--spacing-md);
}
@media (min-width: 480px) {
  .card {
    padding: var(--spacing-md);
  }
  .card-compact {
    padding: var(--spacing-sm);
  }
  .card-hero {
    padding: var(--spacing-lg);
  }
}
@media (min-width: 768px) {
  .card {
    padding: var(--card-padding);
  }
  .card-hero {
    padding: var(--spacing-xl);
  }
}
@media (min-width: 1024px) {
  .card-hero {
    padding: var(--spacing-2xl);
  }
}
.card-grid {
  display: grid;
  gap: var(--spacing-md);
  grid-template-columns: 1fr;
}
@media (min-width: 768px) {
  .card-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
  }
}
@media (min-width: 1024px) {
  .card-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-xl);
  }
}
.card-flex {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}
@media (min-width: 768px) {
  .card-flex {
    flex-direction: row;
    gap: var(--spacing-md);
  }
}
.glass-card {
  background: rgba(0, 0, 0, 0.85);
  -webkit-backdrop-filter: blur(20px);
          backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 1.5rem;
  padding: 2.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}
.glass-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.5), transparent);
}
.glass-card:hover {
  background: rgba(0, 0, 0, 0.9);
  border-color: rgba(212, 175, 55, 0.3);
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.6), 0 0 0 1px rgba(212, 175, 55, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}
.glass-card-strong {
  background: rgba(0, 0, 0, 0.9);
  -webkit-backdrop-filter: blur(25px);
          backdrop-filter: blur(25px);
  border: 1px solid rgba(212, 175, 55, 0.4);
  box-shadow: 0 30px 100px rgba(0, 0, 0, 0.7), 0 0 0 1px rgba(212, 175, 55, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2);
}
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: 0.5rem;
  font-weight: 500;
  font-size: clamp(1rem, 1.5vw, 1.125rem);
  letter-spacing: 2px;
  text-decoration: none;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  white-space: nowrap;
  min-height: 44px;
  min-width: 44px;
  box-sizing: border-box;
  text-transform: uppercase;
}
.btn-primary {
  background: linear-gradient(135deg, var(--primary-gold), var(--secondary-gold));
  color: var(--background-dark);
}
.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(212, 175, 55, 0.3);
}
.btn-secondary {
  background: transparent;
  color: var(--text-primary);
  border: 1px solid var(--border-subtle);
}
.btn-secondary:hover {
  background: var(--background-card);
  border-color: var(--primary-gold);
}
.btn-outline {
  background: transparent;
  color: var(--primary-gold);
  border: 1px solid var(--primary-gold);
}
.btn-outline:hover {
  background: var(--primary-gold);
  color: var(--background-dark);
  border-color: var(--primary-gold);
}
.btn-sm {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: clamp(0.875rem, 1.25vw, 1rem);
  letter-spacing: 2px;
  min-height: 36px;
}
.btn-lg {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: clamp(1.125rem, 1.75vw, 1.25rem);
  letter-spacing: 2px;
  min-height: 52px;
}
@media (max-width: 767px) {
  .btn {
    min-height: 48px;
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: clamp(1rem, 2vw, 1.125rem);
    letter-spacing: 2px;
  }
  .btn-sm {
    min-height: 40px;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: clamp(0.875rem, 1.5vw, 1rem);
    letter-spacing: 2px;
  }
  .btn-lg {
    min-height: 56px;
    padding: var(--spacing-lg) var(--spacing-xl);
    font-size: clamp(1.125rem, 2.5vw, 1.25rem);
    letter-spacing: 2px;
  }
}
.btn-block {
  width: 100%;
  display: flex;
}
.btn-group {
  display: flex;
  gap: var(--spacing-sm);
}
@media (max-width: 767px) {
  .btn-group {
    flex-direction: column;
    gap: var(--spacing-md);
  }
  .btn-group .btn {
    width: 100%;
  }
}
.mb-xs {
  margin-bottom: var(--spacing-xs);
}
.mb-sm {
  margin-bottom: var(--spacing-sm);
}
.mb-md {
  margin-bottom: var(--spacing-md);
}
.mb-lg {
  margin-bottom: var(--spacing-lg);
}
.mt-xs {
  margin-top: var(--spacing-xs);
}
.mt-sm {
  margin-top: var(--spacing-sm);
}
.mt-md {
  margin-top: var(--spacing-md);
}
.mt-lg {
  margin-top: var(--spacing-lg);
}
.p-xs {
  padding: var(--spacing-xs);
}
.p-sm {
  padding: var(--spacing-sm);
}
.p-md {
  padding: var(--spacing-md);
}
.p-lg {
  padding: var(--spacing-lg);
}
.container, .max-w-7xl, .max-w-6xl, .max-w-5xl, .max-w-4xl, .max-w-3xl {
  width: 100% !important;
  max-width: var(--container-max-width) !important;
  margin: 0 auto !important;
  padding: 0 var(--spacing-md) !important;
}
@media (min-width: 768px) {
  .container, .max-w-7xl, .max-w-6xl, .max-w-5xl, .max-w-4xl, .max-w-3xl {
    padding: 0 var(--spacing-lg) !important;
  }
}
.text-gradient-gold {
  background: linear-gradient(135deg, var(--primary-gold), var(--secondary-gold));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
}
.text-gradient-cyber {
  background: linear-gradient(135deg, var(--accent-blue), var(--primary-gold));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
}
.py-24, .py-32, .py-20, .py-16 {
  padding-top: var(--section-padding) !important;
  padding-bottom: var(--section-padding) !important;
}
.mb-20, .mb-16, .mb-12, .mb-8, .mb-6, .mb-4 {
  margin-bottom: var(--spacing-sm) !important;
}
.gap-12, .gap-16, .gap-8, .gap-6, .gap-4 {
  gap: var(--spacing-md) !important;
}
.-mt-8, .-mt-12, .-mt-16 {
  margin-top: 0 !important;
}
.footer-container {
  background: rgba(0, 0, 0, 0.95);
  padding: var(--spacing-2xl) 0;
  border-top: 1px solid var(--border-subtle);
}
.footer-content {
  text-align: center;
}
.footer-logo {
  font-size: clamp(2rem, 4vw, 2.5rem);
  font-weight: 700;
  color: var(--primary-gold);
  margin-bottom: var(--spacing-lg);
  letter-spacing: 3px;
}
.footer-nav {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}
.footer-legal-nav {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
}
.footer-legal-nav a {
  color: var(--text-muted);
  font-size: var(--font-size-sm);
  text-decoration: none;
  transition: color 0.3s ease;
  letter-spacing: 1px;
}
.footer-legal-nav a:hover {
  color: var(--primary-gold);
}
.footer-cta {
  margin-bottom: var(--spacing-xl);
}
.footer-legal {
  color: var(--text-muted);
  font-size: var(--font-size-sm);
  line-height: 1.6;
}
.footer-legal p {
  margin-bottom: var(--spacing-sm);
}
@media (max-width: 767px) {
  .footer-nav {
    gap: var(--spacing-md);
  }
  .footer-legal-nav {
    gap: var(--spacing-sm);
    flex-direction: column;
    align-items: center;
  }
}
.legal-section {
  min-height: 100vh;
  padding: var(--spacing-3xl) 0;
  background: var(--background-dark);
  color: var(--text-primary);
}
.legal-content {
  max-width: 800px;
  margin: 0 auto;
  padding: var(--spacing-xl);
}
.back-button {
  background: transparent;
  border: 1px solid var(--primary-gold);
  color: var(--primary-gold);
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: 4px;
  cursor: pointer;
  margin-bottom: var(--spacing-xl);
  transition: all 0.3s ease;
  font-size: var(--font-size-sm);
}
.back-button:hover {
  background: var(--primary-gold);
  color: var(--background-dark);
}
.legal-text {
  line-height: 1.6;
}
.legal-text h2 {
  color: var(--primary-gold);
  font-size: var(--font-size-xl);
  margin: var(--spacing-xl) 0 var(--spacing-md) 0;
  font-weight: 600;
}
.legal-text p {
  margin-bottom: var(--spacing-md);
  color: var(--text-secondary);
}
.legal-links {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  flex-wrap: wrap;
}
.legal-links a {
  color: var(--primary-gold);
  text-decoration: none;
  font-size: var(--font-size-sm);
  transition: color 0.3s ease;
}
.legal-links a:hover {
  color: var(--secondary-gold);
  text-decoration: underline;
}
@media (max-width: 768px) {
  .legal-content {
    padding: var(--spacing-md);
  }
  .legal-links {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
}
.pointer-events-none {
  pointer-events: none;
}
.visible {
  visibility: visible;
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.absolute {
  position: absolute;
}
.fixed {
  position: fixed;
}
.fixed\! {
  position: fixed !important;
}
.relative {
  position: relative;
}
.static {
  position: static;
}
.sticky {
  position: sticky;
}
.top-1\/2 {
  top: calc(1/2 * 100%);
}
.top-1\/3 {
  top: calc(1/3 * 100%);
}
.top-1\/4 {
  top: calc(1/4 * 100%);
}
.top-3\/4 {
  top: calc(3/4 * 100%);
}
.top-auto {
  top: auto;
}
.right-1\/4 {
  right: calc(1/4 * 100%);
}
.bottom-1\/3 {
  bottom: calc(1/3 * 100%);
}
.bottom-1\/4 {
  bottom: calc(1/4 * 100%);
}
.left-1\/2 {
  left: calc(1/2 * 100%);
}
.left-1\/4 {
  left: calc(1/4 * 100%);
}
.left-3\/4 {
  left: calc(3/4 * 100%);
}
.left-\[-10000px\] {
  left: -10000px;
}
.isolate {
  isolation: isolate;
}
.z-10 {
  z-index: 10;
}
.z-20 {
  z-index: 20;
}
.z-40 {
  z-index: 40;
}
.z-50 {
  z-index: 50;
}
.col-span-1 {
  grid-column: span 1 / span 1;
}
.col-span-2 {
  grid-column: span 2 / span 2;
}
.col-span-3 {
  grid-column: span 3 / span 3;
}
.col-span-4 {
  grid-column: span 4 / span 4;
}
.col-span-5 {
  grid-column: span 5 / span 5;
}
.col-span-6 {
  grid-column: span 6 / span 6;
}
.col-span-12 {
  grid-column: span 12 / span 12;
}
.col-span-full {
  grid-column: 1 / -1;
}
.container {
  width: 100%;
}
.mx-auto {
  margin-inline: auto;
}
.-mb-px {
  margin-bottom: -1px;
}
.ml-auto {
  margin-left: auto;
}
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
.block {
  display: block;
}
.flex {
  display: flex;
}
.grid {
  display: grid;
}
.hidden {
  display: none;
}
.inline {
  display: inline;
}
.inline-block {
  display: inline-block;
}
.inline-flex {
  display: inline-flex;
}
.table {
  display: table;
}
.h-\[1\.2rem\] {
  height: 1.2rem;
}
.h-\[calc\(100vh-4rem\)\] {
  height: calc(100vh - 4rem);
}
.h-full {
  height: 100%;
}
.h-px {
  height: 1px;
}
.max-h-screen {
  max-height: 100vh;
}
.min-h-\[28px\] {
  min-height: 28px;
}
.min-h-\[32px\] {
  min-height: 32px;
}
.min-h-\[40px\] {
  min-height: 40px;
}
.min-h-\[44px\] {
  min-height: 44px;
}
.min-h-\[52px\] {
  min-height: 52px;
}
.min-h-\[100px\] {
  min-height: 100px;
}
.min-h-\[600px\] {
  min-height: 600px;
}
.min-h-screen {
  min-height: 100vh;
}
.w-1\/2 {
  width: calc(1/2 * 100%);
}
.w-1\/3 {
  width: calc(1/3 * 100%);
}
.w-1\/4 {
  width: calc(1/4 * 100%);
}
.w-2\/3 {
  width: calc(2/3 * 100%);
}
.w-3\/4 {
  width: calc(3/4 * 100%);
}
.w-5\/6 {
  width: calc(5/6 * 100%);
}
.w-\[1\.2rem\] {
  width: 1.2rem;
}
.w-full {
  width: 100%;
}
.max-w-full {
  max-width: 100%;
}
.max-w-none {
  max-width: none;
}
.min-w-\[16px\] {
  min-width: 16px;
}
.min-w-\[20px\] {
  min-width: 20px;
}
.min-w-\[24px\] {
  min-width: 24px;
}
.min-w-\[44px\] {
  min-width: 44px;
}
.min-w-full {
  min-width: 100%;
}
.flex-1 {
  flex: 1;
}
.flex-shrink-0 {
  flex-shrink: 0;
}
.grow {
  flex-grow: 1;
}
.-translate-x-full {
  --tw-translate-x: -100%;
  translate: var(--tw-translate-x) var(--tw-translate-y);
}
.-translate-y-1\/2 {
  --tw-translate-y: calc(calc(1/2 * 100%) * -1);
  translate: var(--tw-translate-x) var(--tw-translate-y);
}
.scale-0 {
  --tw-scale-x: 0%;
  --tw-scale-y: 0%;
  --tw-scale-z: 0%;
  scale: var(--tw-scale-x) var(--tw-scale-y);
}
.scale-100 {
  --tw-scale-x: 100%;
  --tw-scale-y: 100%;
  --tw-scale-z: 100%;
  scale: var(--tw-scale-x) var(--tw-scale-y);
}
.scale-110 {
  --tw-scale-x: 110%;
  --tw-scale-y: 110%;
  --tw-scale-z: 110%;
  scale: var(--tw-scale-x) var(--tw-scale-y);
}
.-rotate-90 {
  rotate: calc(90deg * -1);
}
.rotate-0 {
  rotate: 0deg;
}
.rotate-90 {
  rotate: 90deg;
}
.-skew-y-6 {
  --tw-skew-y: skewY(calc(6deg * -1));
  transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
}
.transform {
  transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
}
.cursor-not-allowed {
  cursor: not-allowed;
}
.cursor-pointer {
  cursor: pointer;
}
.touch-manipulation {
  touch-action: manipulation;
}
.resize {
  resize: both;
}
.resize-none {
  resize: none;
}
.resize-x {
  resize: horizontal;
}
.resize-y {
  resize: vertical;
}
.list-inside {
  list-style-position: inside;
}
.list-decimal {
  list-style-type: decimal;
}
.list-disc {
  list-style-type: disc;
}
.appearance-none {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}
.grid-cols-5 {
  grid-template-columns: repeat(5, minmax(0, 1fr));
}
.grid-cols-6 {
  grid-template-columns: repeat(6, minmax(0, 1fr));
}
.grid-cols-12 {
  grid-template-columns: repeat(12, minmax(0, 1fr));
}
.grid-cols-\[repeat\(auto-fit\,minmax\(280px\,1fr\)\)\] {
  grid-template-columns: repeat(auto-fit,minmax(280px,1fr));
}
.flex-col {
  flex-direction: column;
}
.flex-row {
  flex-direction: row;
}
.flex-nowrap {
  flex-wrap: nowrap;
}
.flex-wrap {
  flex-wrap: wrap;
}
.items-center {
  align-items: center;
}
.items-end {
  align-items: flex-end;
}
.items-start {
  align-items: flex-start;
}
.justify-between {
  justify-content: space-between;
}
.justify-center {
  justify-content: center;
}
.justify-end {
  justify-content: flex-end;
}
.justify-start {
  justify-content: flex-start;
}
.divide-y {
  :where(& > :not(:last-child)) {
    --tw-divide-y-reverse: 0;
    border-bottom-style: var(--tw-border-style);
    border-top-style: var(--tw-border-style);
    border-top-width: calc(1px * var(--tw-divide-y-reverse));
    border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  }
}
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.overflow-auto {
  overflow: auto;
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-x-auto {
  overflow-x: auto;
}
.overflow-y-auto {
  overflow-y: auto;
}
.rounded-full {
  border-radius: calc(infinity * 1px);
}
.rounded-none {
  border-radius: 0;
}
.border {
  border-style: var(--tw-border-style);
  border-width: 1px;
}
.border-0 {
  border-style: var(--tw-border-style);
  border-width: 0px;
}
.border-2 {
  border-style: var(--tw-border-style);
  border-width: 2px;
}
.border-4 {
  border-style: var(--tw-border-style);
  border-width: 4px;
}
.border-t {
  border-top-style: var(--tw-border-style);
  border-top-width: 1px;
}
.border-t-4 {
  border-top-style: var(--tw-border-style);
  border-top-width: 4px;
}
.border-r {
  border-right-style: var(--tw-border-style);
  border-right-width: 1px;
}
.border-b {
  border-bottom-style: var(--tw-border-style);
  border-bottom-width: 1px;
}
.border-b-2 {
  border-bottom-style: var(--tw-border-style);
  border-bottom-width: 2px;
}
.border-l-4 {
  border-left-style: var(--tw-border-style);
  border-left-width: 4px;
}
.border-dashed {
  --tw-border-style: dashed;
  border-style: dashed;
}
.border-transparent {
  border-color: transparent;
}
.border-t-transparent {
  border-top-color: transparent;
}
.bg-transparent {
  background-color: transparent;
}
.bg-gradient-to-b {
  --tw-gradient-position: to bottom in oklab;
  background-image: linear-gradient(var(--tw-gradient-stops));
}
.bg-gradient-to-br {
  --tw-gradient-position: to bottom right in oklab;
  background-image: linear-gradient(var(--tw-gradient-stops));
}
.bg-gradient-to-r {
  --tw-gradient-position: to right in oklab;
  background-image: linear-gradient(var(--tw-gradient-stops));
}
.bg-gradient-to-t {
  --tw-gradient-position: to top in oklab;
  background-image: linear-gradient(var(--tw-gradient-stops));
}
.from-transparent {
  --tw-gradient-from: transparent;
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.via-transparent {
  --tw-gradient-via: transparent;
  --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-via-stops);
}
.to-transparent {
  --tw-gradient-to: transparent;
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}
.bg-clip-text {
  -webkit-background-clip: text;
          background-clip: text;
}
.bg-right {
  background-position: right;
}
.bg-no-repeat {
  background-repeat: no-repeat;
}
.object-cover {
  -o-object-fit: cover;
     object-fit: cover;
}
.text-center {
  text-align: center;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}
.leading-none {
  --tw-leading: 1;
  line-height: 1;
}
.break-words {
  overflow-wrap: break-word;
}
.break-all {
  word-break: break-all;
}
.whitespace-nowrap {
  white-space: nowrap;
}
.whitespace-pre-wrap {
  white-space: pre-wrap;
}
.text-transparent {
  color: transparent;
}
.capitalize {
  text-transform: capitalize;
}
.lowercase {
  text-transform: lowercase;
}
.uppercase {
  text-transform: uppercase;
}
.underline {
  text-decoration-line: underline;
}
.opacity-0 {
  opacity: 0%;
}
.opacity-10 {
  opacity: 10%;
}
.opacity-20 {
  opacity: 20%;
}
.opacity-25 {
  opacity: 25%;
}
.opacity-30 {
  opacity: 30%;
}
.opacity-50 {
  opacity: 50%;
}
.opacity-60 {
  opacity: 60%;
}
.opacity-70 {
  opacity: 70%;
}
.opacity-75 {
  opacity: 75%;
}
.opacity-90 {
  opacity: 90%;
}
.opacity-100 {
  opacity: 100%;
}
.shadow-none {
  --tw-shadow: 0 0 #0000;
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
.ring-1 {
  --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
.ring-2 {
  --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
.ring-offset-1 {
  --tw-ring-offset-width: 1px;
  --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
}
.outline {
  outline-style: var(--tw-outline-style);
  outline-width: 1px;
}
.filter {
  filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
}
.backdrop-filter {
  -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
  backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
}
.transition {
  transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
  transition-timing-function: var(--tw-ease, ease);
  transition-duration: var(--tw-duration, 0s);
}
.transition-all {
  transition-property: all;
  transition-timing-function: var(--tw-ease, ease);
  transition-duration: var(--tw-duration, 0s);
}
.transition-colors {
  transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
  transition-timing-function: var(--tw-ease, ease);
  transition-duration: var(--tw-duration, 0s);
}
.transition-opacity {
  transition-property: opacity;
  transition-timing-function: var(--tw-ease, ease);
  transition-duration: var(--tw-duration, 0s);
}
.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: var(--tw-ease, ease);
  transition-duration: var(--tw-duration, 0s);
}
.transition-transform {
  transition-property: transform, translate, scale, rotate;
  transition-timing-function: var(--tw-ease, ease);
  transition-duration: var(--tw-duration, 0s);
}
.duration-200 {
  --tw-duration: 200ms;
  transition-duration: 200ms;
}
.duration-300 {
  --tw-duration: 300ms;
  transition-duration: 300ms;
}
.duration-500 {
  --tw-duration: 500ms;
  transition-duration: 500ms;
}
.duration-1000 {
  --tw-duration: 1000ms;
  transition-duration: 1000ms;
}
.group-hover\:w-full {
  &:is(:where(.group):hover *) {
    @media (hover: hover) {
      width: 100%;
    }
  }
}
.group-hover\:translate-x-full {
  &:is(:where(.group):hover *) {
    @media (hover: hover) {
      --tw-translate-x: 100%;
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
}
.group-hover\:scale-110 {
  &:is(:where(.group):hover *) {
    @media (hover: hover) {
      --tw-scale-x: 110%;
      --tw-scale-y: 110%;
      --tw-scale-z: 110%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }
}
.group-hover\:skew-y-6 {
  &:is(:where(.group):hover *) {
    @media (hover: hover) {
      --tw-skew-y: skewY(6deg);
      transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
    }
  }
}
.before\:absolute {
  &::before {
    content: var(--tw-content);
    position: absolute;
  }
}
.before\:translate-x-\[-100\%\] {
  &::before {
    content: var(--tw-content);
    --tw-translate-x: -100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
}
.before\:bg-gradient-to-r {
  &::before {
    content: var(--tw-content);
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
}
.before\:from-transparent {
  &::before {
    content: var(--tw-content);
    --tw-gradient-from: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
}
.before\:to-transparent {
  &::before {
    content: var(--tw-content);
    --tw-gradient-to: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
}
.before\:transition-transform {
  &::before {
    content: var(--tw-content);
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, ease);
    transition-duration: var(--tw-duration, 0s);
  }
}
.before\:duration-700 {
  &::before {
    content: var(--tw-content);
    --tw-duration: 700ms;
    transition-duration: 700ms;
  }
}
.hover\:scale-105 {
  &:hover {
    @media (hover: hover) {
      --tw-scale-x: 105%;
      --tw-scale-y: 105%;
      --tw-scale-z: 105%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }
}
.hover\:scale-110 {
  &:hover {
    @media (hover: hover) {
      --tw-scale-x: 110%;
      --tw-scale-y: 110%;
      --tw-scale-z: 110%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }
}
.hover\:scale-\[1\.02\] {
  &:hover {
    @media (hover: hover) {
      scale: 1.02;
    }
  }
}
.hover\:no-underline {
  &:hover {
    @media (hover: hover) {
      text-decoration-line: none;
    }
  }
}
.hover\:underline {
  &:hover {
    @media (hover: hover) {
      text-decoration-line: underline;
    }
  }
}
.hover\:opacity-70 {
  &:hover {
    @media (hover: hover) {
      opacity: 70%;
    }
  }
}
.hover\:opacity-80 {
  &:hover {
    @media (hover: hover) {
      opacity: 80%;
    }
  }
}
.hover\:before\:translate-x-\[100\%\] {
  &:hover {
    @media (hover: hover) {
      &::before {
        content: var(--tw-content);
        --tw-translate-x: 100%;
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
    }
  }
}
.focus\:h-auto {
  &:focus {
    height: auto;
  }
}
.focus\:w-auto {
  &:focus {
    width: auto;
  }
}
.focus\:overflow-visible {
  &:focus {
    overflow: visible;
  }
}
.focus\:border-transparent {
  &:focus {
    border-color: transparent;
  }
}
.focus\:ring-0 {
  &:focus {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
}
.focus\:ring-2 {
  &:focus {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
}
.focus\:ring-offset-1 {
  &:focus {
    --tw-ring-offset-width: 1px;
    --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }
}
.focus\:ring-offset-2 {
  &:focus {
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }
}
.focus\:outline-none {
  &:focus {
    --tw-outline-style: none;
    outline-style: none;
  }
}
.focus-visible\:ring-2 {
  &:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
}
.focus-visible\:outline-none {
  &:focus-visible {
    --tw-outline-style: none;
    outline-style: none;
  }
}
.active\:scale-95 {
  &:active {
    --tw-scale-x: 95%;
    --tw-scale-y: 95%;
    --tw-scale-z: 95%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }
}
.active\:scale-\[0\.98\] {
  &:active {
    scale: 0.98;
  }
}
.disabled\:transform-none {
  &:disabled {
    transform: none;
  }
}
.disabled\:cursor-not-allowed {
  &:disabled {
    cursor: not-allowed;
  }
}
.disabled\:opacity-50 {
  &:disabled {
    opacity: 50%;
  }
}
.dark\:scale-0 {
  @media (prefers-color-scheme: dark) {
    --tw-scale-x: 0%;
    --tw-scale-y: 0%;
    --tw-scale-z: 0%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }
}
.dark\:scale-100 {
  @media (prefers-color-scheme: dark) {
    --tw-scale-x: 100%;
    --tw-scale-y: 100%;
    --tw-scale-z: 100%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }
}
.dark\:-rotate-90 {
  @media (prefers-color-scheme: dark) {
    rotate: calc(90deg * -1);
  }
}
.dark\:rotate-0 {
  @media (prefers-color-scheme: dark) {
    rotate: 0deg;
  }
}
.\[\&\>\*\:first-child\]\:rounded-r-none {
  &>*:first-child {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
}
.\[\&\>\*\:first-child\]\:rounded-b-none {
  &>*:first-child {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
  }
}
.\[\&\>\*\:last-child\]\:rounded-t-none {
  &>*:last-child {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }
}
.\[\&\>\*\:last-child\]\:rounded-l-none {
  &>*:last-child {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
}
.\[\&\>\*\:not\(\:first-child\)\]\:mt-\[-1px\] {
  &>*:not(:first-child) {
    margin-top: -1px;
  }
}
.\[\&\>\*\:not\(\:first-child\)\]\:ml-\[-1px\] {
  &>*:not(:first-child) {
    margin-left: -1px;
  }
}
.\[\&\>\*\:not\(\:first-child\)\:not\(\:last-child\)\]\:rounded-none {
  &>*:not(:first-child):not(:last-child) {
    border-radius: 0;
  }
}
:root {
  --gold-primary: #fbbf24;
  --gold-secondary: #f59e0b;
  --cyber-blue: #06b6d4;
  --cyber-purple: #8b5cf6;
}
* {
  box-sizing: border-box;
}
body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #000;
  color: #fff;
  overflow-x: hidden;
  letter-spacing: 2px;
  font-size: clamp(1rem, 1.5vw, 1.125rem);
}
h1, h2, h3, h4, h5, h6 {
  letter-spacing: 2px;
  font-weight: 600;
  line-height: 1.2;
}
p, span, div, a, button, input, textarea, label {
  letter-spacing: 2px;
}
.section-title {
  font-size: clamp(2rem, 6vw, 3rem);
  font-weight: 600;
  letter-spacing: 2px;
  color: #fbbf24;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}
.section-subtitle {
  font-size: clamp(1.25rem, 2.5vw, 1.5rem);
  letter-spacing: 2px;
  color: #e5e7eb;
  margin-bottom: 2rem;
  line-height: 1.6;
}
.card-title {
  font-size: clamp(1.5rem, 3vw, 1.75rem);
  font-weight: 600;
  letter-spacing: 2px;
  color: #fbbf24;
  margin-bottom: 1rem;
}
.card-description {
  font-size: clamp(1.125rem, 2vw, 1.25rem);
  letter-spacing: 2px;
  color: #e5e7eb;
  line-height: 1.6;
  margin-bottom: 1rem;
}
.feature-text {
  font-size: clamp(1rem, 1.75vw, 1.125rem);
  letter-spacing: 2px;
  color: #e5e7eb;
  line-height: 1.5;
}
.label-text {
  font-size: clamp(1rem, 1.5vw, 1.125rem);
  letter-spacing: 2px;
  color: #9ca3af;
  font-weight: 500;
  text-transform: uppercase;
}
@media (max-width: 480px) {
  body {
    font-size: clamp(1rem, 3vw, 1.125rem);
    letter-spacing: 1.5px;
  }
  .section-title {
    font-size: clamp(1.75rem, 8vw, 2.5rem);
    letter-spacing: 1.5px;
  }
  .section-subtitle {
    font-size: clamp(1.125rem, 4vw, 1.25rem);
    letter-spacing: 1.5px;
  }
  .card-title {
    font-size: clamp(1.25rem, 5vw, 1.5rem);
    letter-spacing: 1.5px;
  }
  .card-description {
    font-size: clamp(1rem, 3.5vw, 1.125rem);
    letter-spacing: 1.5px;
  }
  .feature-text {
    font-size: clamp(0.875rem, 3vw, 1rem);
    letter-spacing: 1.5px;
  }
  .label-text {
    font-size: clamp(0.875rem, 2.5vw, 1rem);
    letter-spacing: 1.5px;
  }
}
@media (min-width: 481px) and (max-width: 768px) {
  body {
    font-size: clamp(1.125rem, 2.5vw, 1.25rem);
    letter-spacing: 2px;
  }
  .section-title {
    font-size: clamp(2rem, 7vw, 2.75rem);
    letter-spacing: 2px;
  }
  .section-subtitle {
    font-size: clamp(1.25rem, 3.5vw, 1.375rem);
    letter-spacing: 2px;
  }
}
@media (min-width: 769px) {
  body {
    font-size: clamp(1.125rem, 2vw, 1.25rem);
    letter-spacing: 2px;
  }
}
.text-xs, .text-sm, .text-base, .text-lg, .text-xl, .text-2xl, .text-3xl, .text-4xl, .text-5xl, .text-6xl {
  letter-spacing: 2px !important;
}
.text-xs {
  font-size: clamp(1rem, 1.25vw, 1.125rem) !important;
}
.text-sm {
  font-size: clamp(1.125rem, 1.5vw, 1.25rem) !important;
}
.text-base {
  font-size: clamp(1.125rem, 2vw, 1.25rem) !important;
}
.text-lg {
  font-size: clamp(1.25rem, 2.5vw, 1.5rem) !important;
}
.text-xl {
  font-size: clamp(1.5rem, 3vw, 1.75rem) !important;
}
.text-2xl {
  font-size: clamp(1.75rem, 3.5vw, 1rem) !important;
  font-weight: bold !important;
  letter-spacing: 3px !important;
}
.text-3xl {
  font-size: clamp(2rem, 4vw, 2.5rem) !important;
}
.text-4xl {
  font-size: clamp(2.25rem, 5vw, 3rem) !important;
  margin-top: 5px !important;
}
.text-5xl {
  font-size: clamp(2.5rem, 6vw, 3.5rem) !important;
}
.text-6xl {
  font-size: clamp(3rem, 8vw, 4rem) !important;
}
.gold-gradient-text, .text-gradient-gold, .text-gradient-cyber {
  letter-spacing: 2px !important;
}
button, .btn, .btn-primary, .btn-secondary {
  letter-spacing: 2px !important;
  font-size: clamp(1rem, 1.5vw, 1.125rem) !important;
}
input, textarea, select, label {
  letter-spacing: 2px !important;
  font-size: clamp(1rem, 1.5vw, 1.125rem) !important;
}
table, th, td {
  letter-spacing: 2px !important;
}
nav a, .nav-link {
  letter-spacing: 2px !important;
  font-size: clamp(1.125rem, 2vw, 1.25rem) !important;
}
.py-24 {
  padding-top: 3rem !important;
  padding-bottom: 3rem !important;
}
.py-32 {
  padding-top: 4rem !important;
  padding-bottom: 4rem !important;
}
.py-20 {
  padding-top: 2.5rem !important;
  padding-bottom: 2.5rem !important;
}
.mb-20 {
  margin-bottom: 2rem !important;
}
.mb-16 {
  margin-bottom: 1.5rem !important;
}
section {
  width: 100%;
  max-width: none;
}
.grid {
  display: grid;
  width: 100%;
}
.gap-xl {
  gap: var(--spacing-xl);
}
.gap-lg {
  gap: var(--spacing-lg);
}
.space-y-lg > * + * {
  margin-top: var(--spacing-lg);
}
.mt-lg {
  margin-top: var(--spacing-lg);
}
.mt-md {
  margin-top: var(--spacing-md);
}
.grid-cols-1 {
  grid-template-columns: 1fr;
}
.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}
.grid-cols-3 {
  grid-template-columns: repeat(3, 1fr);
}
.col-span-2 {
  grid-column: span 2 / span 2;
}
@media (min-width: 768px) {
  .md\\:grid-cols-2 {
    grid-template-columns: repeat(2, 1fr);
  }
  .md\\:grid-cols-3 {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media (min-width: 1024px) {
  .lg\\:grid-cols-2 {
    grid-template-columns: repeat(2, 1fr);
  }
  .lg\\:grid-cols-3 {
    grid-template-columns: repeat(3, 1fr);
  }
  .lg\\:grid-cols-12 {
    grid-template-columns: repeat(12, 1fr);
  }
  .lg\\:col-span-2 {
    grid-column: span 2 / span 2;
  }
}
.text-gradient-gold {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
.text-gradient-cyber {
  background: linear-gradient(135deg, #06b6d4, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
.text-gold {
  color: var(--gold-primary) !important;
}
.text-white {
  color: #ffffff !important;
}
.animated-gradient {
  background: linear-gradient(45deg, #fbbf24, #f59e0b, #fbbf24);
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
.aureus-container {
  position: relative;
  background: linear-gradient(135deg, #FFD700, #00D4FF, #8B5CF6, #FF006E, #00FF88);
  border-radius: 24px;
  padding: 3px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  transition: all 0.3s ease;
}
.aureus-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.5);
}
.aureus-container-inner {
  background: rgba(17, 17, 24, 0.95);
  border-radius: 21px;
  padding: 32px;
  position: relative;
  z-index: 1;
  height: 100%;
}
.aureus-table-container {
  position: relative;
  background: linear-gradient(90deg, #FFD700, #00D4FF, #8B5CF6, #FF006E);
  border-radius: 16px;
  padding: 2px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}
.aureus-table-container:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.4);
}
.aureus-table-inner {
  background: rgba(17, 17, 24, 0.95);
  border-radius: 14px;
  overflow: hidden;
  position: relative;
  z-index: 1;
}
.glow-gold {
  box-shadow: 0 0 20px rgba(251, 191, 36, 0.3);
}
.glow-cyber {
  box-shadow: 0 0 20px rgba(6, 182, 212, 0.3);
}
.btn-primary {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: #000;
  font-weight: 600;
  padding: 12px 24px;
  border-radius: 12px;
  text-decoration: none;
  display: inline-block;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}
.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(251, 191, 36, 0.4);
}
.float-animation {
  animation: float 6s ease-in-out infinite;
}
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}
.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite alternate;
}
@keyframes pulse-glow {
  from {
    box-shadow: 0 0 20px rgba(251, 191, 36, 0.3);
  }
  to {
    box-shadow: 0 0 30px rgba(251, 191, 36, 0.6);
  }
}
.slide-in-right {
  animation: slideInRight 1s ease-out;
}
.slide-in-left {
  animation: slideInLeft 1s ease-out;
}
.fade-in-section {
  animation: fadeIn 1s ease-out;
}
@keyframes slideInRight {
  from {
    transform: translateX(100px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
@keyframes slideInLeft {
  from {
    transform: translateX(-100px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
.gold-text {
  color: #fbbf24;
}
.gold-bg {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
}
.admin-dashboard {
  background: #000;
  color: #fff;
}
.admin-dashboard input,
.admin-dashboard textarea,
.admin-dashboard select {
  background-color: #374151 !important;
  border-color: #4b5563 !important;
  color: #ffffff !important;
}
.admin-dashboard input::-moz-placeholder, .admin-dashboard textarea::-moz-placeholder {
  color: #9ca3af !important;
}
.admin-dashboard input::placeholder,
.admin-dashboard textarea::placeholder {
  color: #9ca3af !important;
}
.admin-dashboard label {
  color: #d1d5db !important;
}
.admin-dashboard .bg-white {
  background-color: #1f2937 !important;
}
.admin-dashboard .text-gray-900 {
  color: #ffffff !important;
}
.admin-dashboard .text-gray-700 {
  color: #d1d5db !important;
}
.admin-dashboard .text-gray-500 {
  color: #9ca3af !important;
}
.admin-dashboard .border-gray-300 {
  border-color: #4b5563 !important;
}
.admin-dashboard .border-gray-200 {
  border-color: #374151 !important;
}
.calculator-container {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  padding: 0;
  overflow: hidden;
}
.calculator-layout {
  display: flex;
  flex-direction: row;
  gap: 1rem;
  width: 100%;
}
.calculator-inputs {
  flex: 1;
  max-width: 400px;
  min-width: 300px;
}
.calculator-results {
  flex: 1;
  min-width: 300px;
}
@media (min-width: 768px) and (max-width: 1023px) {
  .calculator-layout {
    gap: 0.75rem;
  }
  .calculator-inputs {
    max-width: 350px;
    min-width: 280px;
  }
  .calculator-results {
    min-width: 280px;
  }
}
@media (min-width: 768px) {
  .calculator-layout {
    flex-direction: row;
    gap: 1rem;
  }
  .calculator-inputs {
    max-width: 400px;
    min-width: 300px;
  }
  .calculator-results {
    min-width: 300px;
  }
}
.calculator-layout {
  flex-direction: column;
  gap: 1.5rem;
}
.calculator-inputs,
.calculator-results {
  max-width: 100%;
  min-width: unset;
}
@media (max-width: 479px) {
  .calculator-container {
    padding: 0 0.5rem;
  }
  .calculator-layout {
    gap: 1rem;
  }
}
.calculator-input-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 0.375rem;
  margin-bottom: 0.5rem;
}
.calculator-input-label {
  font-size: clamp(1.125rem, 2vw, 1.25rem);
  font-weight: 500;
  color: #e5e7eb;
  letter-spacing: 2px;
  flex: 1;
  margin-right: 0.5rem;
}
.calculator-input-field {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin-left: 0.5rem;
  min-width: 0;
  flex-shrink: 0;
}
.calculator-input-field input,
.calculator-input-field select {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0.25rem;
  color: white;
  padding: 0.375rem 0.5rem;
  font-size: clamp(1.125rem, 2vw, 1.25rem);
  letter-spacing: 2px;
  width: 80px;
  min-width: 60px;
  max-width: 120px;
}
.calculator-input-field input:focus,
.calculator-input-field select:focus {
  outline: none;
  border-color: #fbbf24;
  box-shadow: 0 0 0 1px #fbbf24;
}
.calculator-input-unit {
  font-size: clamp(1rem, 1.5vw, 1.125rem);
  color: #9ca3af;
  letter-spacing: 2px;
  white-space: nowrap;
  margin-left: 0.25rem;
}
@media (max-width: 768px) {
  .calculator-input-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
    padding: 0.75rem;
  }
  .calculator-input-label {
    margin-right: 0;
    margin-bottom: 0.25rem;
  }
  .calculator-input-field {
    margin-left: 0;
    width: 100%;
    justify-content: space-between;
  }
  .calculator-input-field input,
  .calculator-input-field select {
    width: 120px;
    max-width: 150px;
  }
}
@media (max-width: 480px) {
  .calculator-input-field input,
  .calculator-input-field select {
    width: 100px;
    max-width: 120px;
    font-size: 0.8rem;
  }
  .calculator-input-label {
    font-size: clamp(1rem, 2vw, 1.125rem);
    letter-spacing: 2px;
  }
  .calculator-input-unit {
    font-size: clamp(0.875rem, 1.5vw, 1rem);
    letter-spacing: 2px;
  }
}
.calculator-header {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
}
.calculator-header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1rem;
}
.calculator-title-section {
  flex: 1;
}
.calculator-reset-button {
  flex-shrink: 0;
}
@media (min-width: 640px) {
  .calculator-header {
    flex-direction: row;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 1rem;
  }
  .calculator-header-content {
    width: 100%;
  }
}
@media (max-width: 639px) {
  .calculator-header-content {
    flex-direction: column;
    align-items: flex-start;
  }
  .calculator-reset-button {
    align-self: flex-end;
  }
}
.calculator-results-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.75rem;
}
@media (min-width: 640px) {
  .calculator-results-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 480px) {
  .calculator-results-grid {
    gap: 0.5rem;
  }
}
@media (max-width: 768px) {
  .calculator-container .btn {
    min-height: 44px;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }
  .calculator-container .card {
    padding: 1rem;
  }
  .calculator-container h3 {
    font-size: 1.125rem;
  }
  .calculator-container p {
    font-size: 0.875rem;
  }
}
@media (max-width: 480px) {
  .calculator-container {
    margin: 0 -0.5rem;
  }
  .calculator-container .btn {
    min-height: 48px;
    padding: 0.875rem 1.25rem;
    font-size: 0.9rem;
    border-radius: 0.5rem;
  }
  .calculator-container .card {
    padding: 0.875rem;
    border-radius: 0.75rem;
  }
  .calculator-container h3 {
    font-size: 1rem;
    line-height: 1.4;
  }
  .calculator-container p {
    font-size: 0.8rem;
    line-height: 1.5;
  }
  .calculator-layout {
    gap: 1.25rem;
  }
  .calculator-inputs .space-y-4 > * + * {
    margin-top: 1.25rem;
  }
  .calculator-results .space-y-4 > * + * {
    margin-top: 1.25rem;
  }
}
.calculator-projection-table {
  overflow-x: auto;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 0.375rem;
  -webkit-overflow-scrolling: touch;
}
.calculator-projection-table table {
  width: 100%;
  font-size: clamp(1.125rem, 2vw, 1.25rem);
  letter-spacing: 2px;
  min-width: 600px;
}
.calculator-projection-table th,
.calculator-projection-table td {
  padding: 0.75rem;
  white-space: nowrap;
}
@media (max-width: 768px) {
  .calculator-projection-table {
    margin: 0 -1rem;
    border-radius: 0;
  }
  .calculator-projection-table table {
    font-size: 0.8rem;
    min-width: 500px;
  }
  .calculator-projection-table th,
  .calculator-projection-table td {
    padding: 0.5rem 0.375rem;
  }
}
@media (max-width: 480px) {
  .calculator-projection-table {
    margin: 0 -0.875rem;
  }
  .calculator-projection-table table {
    font-size: 0.75rem;
    min-width: 450px;
  }
  .calculator-projection-table th,
  .calculator-projection-table td {
    padding: 0.375rem 0.25rem;
  }
}
.responsive-table-container {
  overflow-x: auto;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 0.5rem;
  -webkit-overflow-scrolling: touch;
  margin: var(--spacing-md) 0;
}
.responsive-table {
  width: 100%;
  border-collapse: collapse;
  font-size: clamp(1.125rem, 2vw, 1.25rem);
  letter-spacing: 2px;
  min-width: 600px;
}
.responsive-table th,
.responsive-table td {
  padding: var(--spacing-sm) var(--spacing-md);
  text-align: left;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  white-space: nowrap;
}
.responsive-table th {
  background: rgba(0, 0, 0, 0.3);
  font-weight: 600;
  color: #fbbf24;
  position: sticky;
  top: 0;
  z-index: 1;
}
.responsive-table td {
  color: #ffffff;
}
.responsive-table tr:hover {
  background: rgba(255, 255, 255, 0.05);
}
.financial-table {
  min-width: 700px;
}
.financial-table .currency {
  text-align: right;
  font-family: 'Courier New', monospace;
}
.financial-table .total-row {
  background: rgba(251, 191, 36, 0.1);
  font-weight: 600;
}
.financial-table .total-row td {
  border-top: 2px solid #fbbf24;
  color: #fbbf24;
}
@media (max-width: 768px) {
  .responsive-table-container {
    margin: var(--spacing-sm) -var(--spacing-md);
    border-radius: 0;
  }
  .responsive-table {
    font-size: 0.8rem;
    min-width: 500px;
  }
  .responsive-table th,
  .responsive-table td {
    padding: var(--spacing-xs) var(--spacing-sm);
  }
  .financial-table {
    min-width: 600px;
  }
}
@media (max-width: 480px) {
  .responsive-table-container {
    margin: var(--spacing-sm) -var(--spacing-sm);
  }
  .responsive-table {
    font-size: 0.75rem;
    min-width: 450px;
  }
  .responsive-table th,
  .responsive-table td {
    padding: var(--spacing-xs);
  }
  .financial-table {
    min-width: 500px;
  }
}
.hero-section {
  min-height: 50vh;
  display: flex;
  align-items: center;
  position: relative;
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  padding: var(--spacing-md) 0;
}
.hero-content-wrapper {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  width: 100%;
}
.hero-left-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: var(--spacing-md);
}
.hero-right-content {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: var(--spacing-lg);
}
.hero-stats-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-sm);
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}
.hero-logo {
  height: 3rem;
  width: auto;
  margin-bottom: var(--spacing-sm);
}
.hero-main-logo {
  width: 450px;
  height: auto;
  filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.4));
  animation: float 6s ease-in-out infinite;
}
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}
.hero-buttons {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  width: 100%;
  max-width: 300px;
}
.hero-features {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-md);
}
@media (min-width: 480px) {
  .hero-stats-grid {
    grid-template-columns: repeat(3, 1fr);
    max-width: 500px;
  }
  .hero-buttons {
    flex-direction: row;
    max-width: 400px;
    gap: var(--spacing-md) !important;
    justify-content: center;
  }
  .hero-buttons .btn {
    margin: 0 calc(var(--spacing-md) / 2) !important;
    flex: 0 0 auto;
  }
  .hero-buttons .btn:first-child {
    margin-left: 0 !important;
  }
  .hero-buttons .btn:last-child {
    margin-right: 0 !important;
  }
  .hero-buttons .btn + .btn {
    margin-left: var(--spacing-md) !important;
  }
  .hero-logo {
    height: 4rem;
  }
}
@media (min-width: 480px) and (max-width: 800px) {
  .hero-buttons {
    gap: 1rem !important;
    display: flex !important;
    flex-direction: row !important;
    justify-content: center !important;
    align-items: center !important;
  }
  .hero-buttons .btn {
    margin: 0 0.5rem !important;
    min-width: 140px;
    flex-shrink: 0 !important;
  }
  .hero-buttons .btn:first-child {
    margin-left: 0 !important;
  }
  .hero-buttons .btn:last-child {
    margin-right: 0 !important;
  }
}
@media (min-width: 481px) and (max-width: 799px) {
  .hero-buttons {
    -moz-column-gap: 1rem !important;
         column-gap: 1rem !important;
  }
  .hero-buttons > * + * {
    margin-left: 1rem !important;
  }
}
@media (min-width: 768px) {
  .hero-content {
    flex-direction: row;
    align-items: center;
    gap: var(--spacing-2xl);
  }
  .hero-main-content {
    flex: 1;
    align-items: flex-start;
    text-align: left;
    max-width: none;
  }
  .hero-stats-grid {
    flex: 0 0 auto;
    width: 300px;
    margin: 0;
  }
  .hero-buttons {
    max-width: none;
    width: auto;
  }
  .hero-features {
    justify-content: flex-start;
  }
  .hero-logo {
    margin: 0 0 var(--spacing-sm) 0;
  }
}
@media (min-width: 1024px) {
  .hero-section {
    background-attachment: fixed;
  }
  .hero-content-wrapper {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
    align-items: center;
  }
  .hero-section {
    min-height: 60vh;
    padding: var(--spacing-sm) 0;
  }
  .hero-left-content {
    align-items: flex-start;
    text-align: left;
  }
  .hero-right-content {
    margin-top: 0;
  }
  .hero-stats-grid {
    width: 350px;
  }
  .hero-logo {
    height: 5rem;
  }
  .hero-main-logo {
    width: 550px;
  }
}
@media (min-width: 1200px) {
  .hero-stats-grid {
    width: 400px;
  }
}
.aureus-container-inner {
  padding: 16px;
}
.btn-primary {
  padding: 8px 16px;
  font-size: 12px;
}
.text-gradient-gold,
.text-gradient-cyber {
  font-size: clamp(1.5rem, 5vw, 2.5rem);
}
.aureus-container {
  border-radius: 16px;
  padding: 2px;
}
@media (min-width: 480px) {
  .aureus-container-inner {
    padding: 20px;
  }
  .btn-primary {
    padding: 10px 20px;
    font-size: 14px;
  }
  .text-gradient-gold,
  .text-gradient-cyber {
    font-size: clamp(2rem, 6vw, 3rem);
  }
}
@media (min-width: 768px) {
  .aureus-container-inner {
    padding: 24px;
  }
  .text-gradient-gold,
  .text-gradient-cyber {
    font-size: clamp(2.5rem, 8vw, 4rem);
  }
}
@media (min-width: 1024px) {
  .aureus-container-inner {
    padding: 32px;
  }
}
.section-overlap {
  margin-top: -2rem;
  position: relative;
  z-index: 10;
}
.section-overlap-large {
  margin-top: -4rem;
  position: relative;
  z-index: 10;
}
.visual-separator {
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.3), transparent);
  margin: 3rem 0;
}
.content-flow {
  position: relative;
}
.content-flow::before {
  content: '';
  position: absolute;
  top: -2rem;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 2px;
  background: linear-gradient(90deg, transparent, #FFD700, transparent);
  border-radius: 1px;
}
.section-transition {
  position: relative;
  z-index: 5;
}
.section-transition::before {
  content: '';
  position: absolute;
  top: -50px;
  left: 0;
  right: 0;
  height: 100px;
  background: linear-gradient(to bottom, transparent, rgba(0, 0, 0, 0.1), transparent);
  pointer-events: none;
}
.responsive-grid {
  display: grid;
  gap: 2rem;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}
@media (min-width: 768px) {
  .responsive-grid {
    gap: 2.5rem;
  }
}
@media (min-width: 1024px) {
  .responsive-grid {
    gap: 3rem;
  }
}
.professional-card {
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-origin: center;
}
.professional-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}
.heading-responsive {
  font-size: clamp(2rem, 5vw, 4rem);
  line-height: 1.1;
  letter-spacing: -0.02em;
}
.subheading-responsive {
  font-size: clamp(1.25rem, 3vw, 1.5rem);
  line-height: 1.4;
}
.body-responsive {
  font-size: clamp(1rem, 2vw, 1.125rem);
  line-height: 1.6;
}
::-webkit-scrollbar {
  width: 8px;
}
::-webkit-scrollbar-track {
  background: #1a1a1a;
}
::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  border-radius: 4px;
}
::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #f59e0b, #fbbf24);
}
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.gallery-grid-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-md);
  width: 100%;
}
.gallery-image-item {
  width: 100%;
  max-width: 100%;
}
@media (min-width: 480px) {
  .gallery-grid-container {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
  }
}
@media (min-width: 768px) {
  .gallery-grid-container {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-xl);
  }
}
@media (min-width: 1024px) {
  .gallery-grid-container {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-2xl);
  }
}
@media (min-width: 1200px) {
  .gallery-grid-container {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-2xl);
  }
}
.gallery-flex-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: var(--spacing-md);
}
.gallery-flex-container > div {
  width: 100%;
  max-width: 350px;
  flex: 0 0 auto;
}
@media (min-width: 480px) {
  .gallery-flex-container {
    gap: var(--spacing-lg);
  }
  .gallery-flex-container > div {
    width: calc(50% - var(--spacing-lg) / 2);
    max-width: 300px;
  }
}
@media (min-width: 768px) {
  .gallery-flex-container {
    gap: var(--spacing-xl);
  }
  .gallery-flex-container > div {
    width: calc(33.333% - var(--spacing-xl) * 2 / 3);
    max-width: 280px;
  }
}
@media (min-width: 1024px) {
  .gallery-flex-container > div {
    width: calc(25% - var(--spacing-xl) * 3 / 4);
    max-width: 260px;
  }
}
.gallery-image-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}
.gallery-image-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4), 0 0 20px rgba(251, 191, 36, 0.2);
}
.gallery-image-fade-in {
  animation: galleryFadeIn 0.6s ease-out forwards;
}
@keyframes galleryFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
.gallery-loading-shimmer {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
.footer-container {
  background: #000000;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: var(--spacing-lg) 0;
}
.footer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
  text-align: center;
}
.footer-logo {
  font-size: clamp(1.75rem, 3vw, 2rem);
  font-weight: 800;
  color: #fbbf24;
  letter-spacing: 2px;
  margin-bottom: var(--spacing-sm);
}
.footer-nav {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}
.footer-nav a {
  color: #e5e7eb;
  text-decoration: none;
  font-size: clamp(1.125rem, 2vw, 1.25rem);
  letter-spacing: 2px;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: 0.25rem;
  transition: all 0.3s ease;
}
.footer-nav a:hover {
  color: #fbbf24;
  background: rgba(251, 191, 36, 0.1);
}
.footer-legal-nav {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-md);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}
.footer-legal-nav button {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 0.875rem;
  transition: color 0.3s ease;
}
.footer-legal-nav button:hover {
  color: var(--gold-primary);
}
.footer-cta {
  margin-bottom: var(--spacing-md);
}
.footer-legal {
  padding-top: var(--spacing-md);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  width: 100%;
}
.footer-legal p {
  font-size: 0.75rem;
  color: #9ca3af;
  margin-bottom: var(--spacing-xs);
  line-height: 1.5;
}
@media (min-width: 768px) {
  .footer-container {
    padding: var(--spacing-xl) 0;
  }
  .footer-content {
    gap: var(--spacing-lg);
  }
  .footer-logo {
    font-size: 2rem;
  }
  .footer-nav {
    gap: var(--spacing-md);
  }
  .footer-nav a {
    font-size: 1rem;
    padding: var(--spacing-sm) var(--spacing-md);
  }
  .footer-legal p {
    font-size: 0.875rem;
  }
}
@media (min-width: 1024px) {
  .footer-container {
    padding: var(--spacing-2xl) 0;
  }
  .footer-content {
    gap: var(--spacing-xl);
  }
}
.header-container {
  position: sticky;
  top: 0;
  z-index: 50;
  -webkit-backdrop-filter: blur(20px);
          backdrop-filter: blur(20px);
  background: rgba(0, 0, 0, 0.85);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.header-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) 0;
  position: relative;
  z-index: 10;
}
.header-logo {
  font-size: clamp(1.75rem, 3vw, 2rem);
  font-weight: 800;
  color: #fbbf24;
  letter-spacing: 2px;
  text-decoration: none;
  display: flex;
  flex-direction: column;
  line-height: 1;
}
.header-logo-subtitle {
  font-size: clamp(1rem, 1.5vw, 1.125rem);
  font-weight: 400;
  color: #9ca3af;
  letter-spacing: 2px;
  margin-top: -0.25rem;
}
.header-logo-image {
  text-decoration: none;
  display: flex;
  align-items: center;
}
.header-logo-img {
  height: 2.5rem;
  width: auto;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  transition: all 0.3s ease;
}
.header-logo-img:hover {
  filter: drop-shadow(0 4px 8px rgba(251, 191, 36, 0.4));
}
.desktop-nav {
  display: none;
  align-items: center;
  gap: var(--spacing-lg);
}
.desktop-nav a {
  color: #e5e7eb;
  text-decoration: none;
  font-size: clamp(1.125rem, 2vw, 1.25rem);
  font-weight: 500;
  letter-spacing: 2px;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: 0.375rem;
  transition: all 0.3s ease;
}
.desktop-nav a:hover {
  color: #fbbf24;
  background: rgba(251, 191, 36, 0.1);
}
.desktop-actions {
  display: none;
  align-items: center;
  gap: var(--spacing-md);
}
.desktop-logo,
.desktop-nav-single,
.desktop-actions-single {
  display: none;
}
.mobile-menu-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background: transparent;
  border: none;
  cursor: pointer;
  z-index: 60;
  position: relative;
}
.hamburger-icon {
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
}
.hamburger-line {
  width: 100%;
  height: 2px;
  background: #fbbf24;
  border-radius: 1px;
  transition: all 0.3s ease;
  transform-origin: center;
}
.hamburger-line.open:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}
.hamburger-line.open:nth-child(2) {
  opacity: 0;
}
.hamburger-line.open:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}
.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);
  z-index: 40;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}
.mobile-menu-overlay.open {
  opacity: 1;
  visibility: visible;
}
.mobile-menu-panel {
  position: fixed;
  top: 0;
  right: -100%;
  width: 280px;
  height: 100vh;
  background: rgba(0, 0, 0, 0.95);
  -webkit-backdrop-filter: blur(20px);
          backdrop-filter: blur(20px);
  border-left: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 50;
  transition: right 0.3s ease;
  overflow-y: auto;
}
.mobile-menu-panel.open {
  right: 0;
}
.mobile-menu-content {
  padding: var(--spacing-xl) var(--spacing-lg);
  padding-top: 5rem;
}
.mobile-menu-nav {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}
.mobile-menu-nav a {
  color: #e5e7eb;
  text-decoration: none;
  font-size: 1.125rem;
  font-weight: 500;
  letter-spacing: 2px;
  padding: var(--spacing-md) 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}
.mobile-menu-nav a:hover {
  color: #fbbf24;
  padding-left: var(--spacing-sm);
}
.mobile-menu-actions {
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-lg);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}
@media (min-width: 768px) {
  .mobile-menu-button {
    display: none;
  }
  .header-logo {
    font-size: 1.875rem;
  }
  .header-logo-subtitle {
    font-size: 0.875rem;
  }
  .header-logo-img {
    height: 3rem;
  }
  .desktop-nav {
    display: flex;
  }
  .desktop-actions {
    display: flex;
  }
  .header-nav-top,
  .header-nav-bottom {
    display: none;
  }
}
@media (min-width: 768px) and (max-width: 1023px) {
  .header-nav {
    flex-direction: column;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) 0;
  }
  .header-nav-top {
    display: flex !important;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }
  .header-nav-bottom {
    display: flex !important;
    justify-content: center;
    width: 100%;
  }
  .header-nav > .header-logo-image,
  .header-nav > .desktop-nav,
  .header-nav > .desktop-actions {
    display: none;
  }
  .desktop-nav {
    flex-wrap: wrap;
    justify-content: center;
    gap: var(--spacing-md) var(--spacing-lg);
    max-width: 100%;
  }
  .desktop-nav a {
    font-size: 0.875rem;
    padding: var(--spacing-xs) var(--spacing-sm);
    letter-spacing: 1px;
    white-space: nowrap;
  }
  .desktop-actions {
    flex-shrink: 0;
  }
  .desktop-actions .btn {
    font-size: 0.8rem;
    padding: 0.5rem 0.875rem;
    white-space: nowrap;
  }
}
@media (min-width: 1024px) {
  .header-nav {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 0;
  }
  .tablet-layout {
    display: none !important;
  }
  .desktop-logo,
  .desktop-nav-single,
  .desktop-actions-single {
    display: flex !important;
  }
  .desktop-nav-single {
    gap: var(--spacing-lg);
    flex-wrap: nowrap;
    justify-content: center;
  }
  .desktop-nav-single a {
    font-size: 1rem;
    padding: var(--spacing-sm) var(--spacing-md);
    letter-spacing: 2px;
  }
}
@media (max-width: 480px) {
  .header-logo {
    font-size: 1.25rem;
  }
  .header-logo-subtitle {
    font-size: 0.7rem;
  }
  .header-logo-img {
    height: 2rem;
  }
}
html, body {
  overflow-x: hidden;
  max-width: 100vw;
}
* {
  box-sizing: border-box;
}
img {
  max-width: 100%;
  height: auto;
}
video {
  max-width: 100%;
  height: auto;
}
iframe {
  max-width: 100%;
}
table {
  max-width: 100%;
}
p, h1, h2, h3, h4, h5, h6 {
  word-wrap: break-word;
  overflow-wrap: break-word;
}
@media (max-width: 767px) {
  * {
    animation-duration: 0.3s !important;
    transition-duration: 0.3s !important;
  }
  button, a, input, select, textarea {
    -webkit-tap-highlight-color: rgba(251, 191, 36, 0.3);
  }
  body {
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
  }
}
@media (min-resolution: 192dpi) {
  .card, .btn, .glass-card {
    border-width: 0.5px;
  }
}
@media print {
  .header-container {
    position: static !important;
  }
  body {
    background: white !important;
    color: black !important;
  }
}
.rounded-full {
  margin-right: 15px;
}
.border {
  border-width: 0px;
}
.text-amber-500 {
  margin-right: 5px;
}
.text-green-400 {
  margin-right: 5px;
}
.max-w-2xl {
  max-width: 100rem;
}
.bg-white\/5 {
  background-color: rgb(255 255 255 / 0%) !important;
}
.gold-diggers-card {
  background: rgba(31, 41, 55, 0.6);
  -webkit-backdrop-filter: blur(16px);
          backdrop-filter: blur(16px);
  border: 1px solid rgba(107, 114, 128, 0.5);
  border-radius: 0.75rem;
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
  transition: all 0.3s ease;
  min-height: 400px;
  display: flex;
  flex-direction: column;
}
.gold-diggers-card:hover {
  border-color: rgba(251, 191, 36, 0.3);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 20px rgba(251, 191, 36, 0.1);
}
.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid rgba(107, 114, 128, 0.3);
  flex-shrink: 0;
}
.card-icon {
  font-size: 1.5rem;
  margin-right: var(--spacing-sm);
}
.card-title {
  font-size: clamp(1.5rem, 3vw, 1.75rem);
  font-weight: 700;
  color: #fbbf24;
  letter-spacing: 2px;
  flex: 1;
}
.live-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 600;
}
.live-dot {
  width: 0.5rem;
  height: 0.5rem;
  background: #22c55e;
  border-radius: 50%;
  animation: pulse 2s infinite;
}
.card-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}
.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}
.step-item {
  display: flex;
  gap: var(--spacing-md);
  align-items: flex-start;
  margin-bottom: var(--spacing-lg);
}
.step-number {
  width: 2rem;
  height: 2rem;
  background: #fbbf24;
  color: #000000;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 0.875rem;
  flex-shrink: 0;
}
.step-content {
  flex: 1;
}
.step-title {
  font-weight: 600;
  color: #ffffff;
  margin-bottom: var(--spacing-xs);
}
.step-description {
  color: #d1d5db;
  font-size: 0.875rem;
  line-height: 1.6;
}
.competition-status {
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}
.status-icon {
  font-size: 2.5rem;
  margin-bottom: var(--spacing-sm);
}
.status-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #fbbf24;
  margin-bottom: var(--spacing-sm);
}
.status-description {
  color: #d1d5db;
  margin-bottom: var(--spacing-sm);
}
.status-subtitle {
  color: #fbbf24;
  font-weight: 600;
}
.qualification-badge {
  background: rgba(251, 191, 36, 0.1);
  border: 1px solid rgba(251, 191, 36, 0.3);
  border-radius: 0.5rem;
  padding: var(--spacing-md);
  color: #fbbf24;
  font-weight: 600;
  margin-top: var(--spacing-lg);
}
.prize-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}
.prize-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  border-radius: 0.5rem;
  border: 1px solid;
}
.prize-first {
  background: linear-gradient(to right, rgba(234, 179, 8, 0.2), rgba(245, 158, 11, 0.2));
  border-color: rgba(234, 179, 8, 0.3);
}
.prize-second {
  background: linear-gradient(to right, rgba(156, 163, 175, 0.2), rgba(107, 114, 128, 0.2));
  border-color: rgba(156, 163, 175, 0.3);
}
.prize-third {
  background: linear-gradient(to right, rgba(217, 119, 6, 0.2), rgba(249, 115, 22, 0.2));
  border-color: rgba(217, 119, 6, 0.3);
}
.prize-other {
  background: rgba(55, 65, 81, 0.3);
  border-color: rgba(75, 85, 99, 0.3);
}
.prize-rank {
  font-weight: 600;
  color: #ffffff;
}
.prize-amount {
  font-weight: 700;
  color: #fbbf24;
  font-size: 1.125rem;
}
.prize-note {
  font-size: 0.875rem;
  color: #9ca3af;
  text-align: center;
  margin-top: var(--spacing-md);
  padding: var(--spacing-sm);
  background: rgba(31, 41, 55, 0.5);
  border-radius: 0.5rem;
}
.stat-card {
  background: rgba(31, 41, 55, 0.6);
  -webkit-backdrop-filter: blur(16px);
          backdrop-filter: blur(16px);
  border: 1px solid rgba(107, 114, 128, 0.5);
  border-radius: 0.75rem;
  padding: var(--spacing-xl);
  text-align: center;
  transition: all 0.3s ease;
  margin-bottom: var(--spacing-lg);
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.stat-card:hover {
  border-color: rgba(251, 191, 36, 0.3);
}
.stat-number {
  font-size: clamp(2.25rem, 4vw, 2.5rem);
  font-weight: 900;
  color: #fbbf24;
  letter-spacing: 2px;
  margin-bottom: var(--spacing-xs);
}
.stat-label {
  color: #d1d5db;
  font-size: clamp(1.125rem, 2vw, 1.25rem);
  text-transform: uppercase;
  letter-spacing: 2px;
  font-weight: 500;
}
.btn-gold-diggers {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg) var(--spacing-xl);
  background: linear-gradient(to right, #fbbf24, #f59e0b);
  color: #000000;
  font-weight: 700;
  font-size: 1.125rem;
  border-radius: 0.75rem;
  text-decoration: none;
  transition: all 0.3s ease;
  transform-origin: center;
}
.btn-gold-diggers:hover {
  background: linear-gradient(to right, #f59e0b, #fbbf24);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 20px rgba(251, 191, 36, 0.3);
  transform: scale(1.05);
}
.btn-gold-diggers:active {
  transform: scale(0.95);
}
.btn-gold-diggers:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(251, 191, 36, 0.5), 0 0 0 4px rgba(0, 0, 0, 1);
}
#gold-diggers-club .grid {
  gap: var(--spacing-xl) !important;
  margin-bottom: var(--spacing-xl);
}
#gold-diggers-club .grid-cols-2 {
  grid-template-columns: 1fr 1fr;
}
#gold-diggers-club .grid-cols-3 {
  grid-template-columns: 2fr 1fr;
}
#gold-diggers-club .col-span-2 {
  grid-column: span 1;
}
#gold-diggers-club .space-y-lg > * + * {
  margin-top: var(--spacing-lg);
}
.competition-status {
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}
.qualification-badge {
  background: rgba(251, 191, 36, 0.1);
  border: 1px solid rgba(251, 191, 36, 0.3);
  border-radius: 0.5rem;
  padding: var(--spacing-md);
  margin-top: var(--spacing-lg);
  color: #fbbf24;
  font-weight: 600;
  text-align: center;
}
@media (max-width: 768px) {
  #gold-diggers-club .grid-cols-2,
  #gold-diggers-club .grid-cols-3 {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg) !important;
  }
  #gold-diggers-club .col-span-2 {
    grid-column: span 1;
  }
  .gold-diggers-card {
    min-height: auto;
    margin-bottom: var(--spacing-md);
  }
  .stat-card {
    min-height: 100px;
    margin-bottom: var(--spacing-md);
  }
}
.calculator-layout-modern {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-xl);
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  overflow-x: hidden;
}
@media (max-width: 480px) {
  .calculator-layout-modern {
    max-width: 100vw !important;
    width: 100vw !important;
    margin: 0 !important;
    padding: 0 !important;
    gap: var(--spacing-md) !important;
  }
}
@media (max-width: 360px) {
  .calculator-layout-modern {
    gap: var(--spacing-sm) !important;
  }
}
#calculator {
  overflow-x: hidden;
  width: 100%;
}
#calculator .container {
  max-width: 100%;
  overflow-x: hidden;
  padding-left: var(--spacing-md);
  padding-right: var(--spacing-md);
}
@media (max-width: 480px) {
  #calculator .container {
    padding-left: var(--spacing-sm);
    padding-right: var(--spacing-sm);
  }
}
@media (max-width: 360px) {
  #calculator .container {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }
}
.calculator-inputs-modern {
  background: rgba(17, 24, 39, 0.6);
  border: 1px solid rgba(107, 114, 128, 0.3);
  border-radius: 1rem;
  padding: var(--spacing-xl);
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
}
.inputs-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}
.modern-inputs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-lg);
}
@media (max-width: 768px) {
  .calculator-layout-modern {
    gap: var(--spacing-lg);
    padding: 0 var(--spacing-sm);
  }
  .calculator-inputs-modern {
    padding: var(--spacing-lg);
    margin: 0 -var(--spacing-xs);
  }
  .modern-inputs-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  .input-group {
    padding: var(--spacing-sm);
  }
  .input-label {
    font-size: 0.8rem;
  }
  .modern-input {
    padding: var(--spacing-sm);
    font-size: 0.9rem;
  }
}
@media (max-width: 480px) {
  .calculator-inputs-modern {
    padding: var(--spacing-sm) !important;
    margin: 0 !important;
    border-radius: 0.75rem;
  }
  .calculator-inputs-modern .inputs-header h3,
  .inputs-header h3.text-xl {
    font-size: 1rem !important;
    line-height: 1.3 !important;
  }
  .calculator-inputs-modern .inputs-header p,
  .inputs-header p.text-sm {
    font-size: 0.75rem !important;
    line-height: 1.4 !important;
  }
  .calculator-inputs-modern .input-group {
    padding: var(--spacing-xs) !important;
    margin-bottom: var(--spacing-xs) !important;
  }
  .calculator-inputs-modern .input-group.editable,
  .calculator-inputs-modern .input-group.readonly {
    padding: var(--spacing-sm) !important;
  }
  .calculator-inputs-modern .input-label {
    font-size: 0.7rem !important;
    margin-bottom: 0.25rem !important;
    line-height: 1.2 !important;
  }
  .calculator-inputs-modern .modern-input {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.8rem !important;
    min-height: 40px !important;
  }
  .calculator-inputs-modern .modern-input select {
    font-size: 0.8rem !important;
  }
}
@media (max-width: 360px) {
  .calculator-inputs-modern {
    padding: 0.75rem !important;
    margin: 0 -0.25rem !important;
  }
  .calculator-inputs-modern .inputs-header h3,
  .inputs-header h3.text-xl {
    font-size: 0.9rem !important;
    margin-bottom: 0.5rem !important;
  }
  .calculator-inputs-modern .inputs-header p,
  .inputs-header p.text-sm {
    font-size: 0.7rem !important;
    margin-bottom: 1rem !important;
  }
  .calculator-inputs-modern .input-group {
    padding: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }
  .calculator-inputs-modern .input-group.editable,
  .calculator-inputs-modern .input-group.readonly {
    padding: 0.625rem !important;
  }
  .calculator-inputs-modern .input-label {
    font-size: 0.65rem !important;
    margin-bottom: 0.2rem !important;
    letter-spacing: 0.02em !important;
  }
  .calculator-inputs-modern .modern-input {
    padding: 0.4rem 0.6rem !important;
    font-size: 0.75rem !important;
    min-height: 36px !important;
  }
  .calculator-inputs-modern .modern-input select {
    font-size: 0.75rem !important;
  }
  .calculator-inputs-modern .modern-inputs-grid {
    gap: 0.75rem !important;
  }
}
@media (max-width: 480px) {
  #calculator .text-xl,
  #calculator h3.text-xl {
    font-size: 1rem !important;
  }
  #calculator .text-sm,
  #calculator p.text-sm {
    font-size: 0.75rem !important;
  }
  #calculator .mb-2 {
    margin-bottom: 0.5rem !important;
  }
  #calculator .mb-6 {
    margin-bottom: 1rem !important;
  }
}
@media (max-width: 360px) {
  #calculator .text-xl,
  #calculator h3.text-xl {
    font-size: 0.9rem !important;
  }
  #calculator .text-sm,
  #calculator p.text-sm {
    font-size: 0.7rem !important;
  }
}
@media (max-width: 480px) {
  #calculator {
    width: 100vw !important;
    max-width: 100vw !important;
    overflow-x: hidden !important;
  }
  #calculator .container {
    width: 100% !important;
    max-width: 100% !important;
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
    margin: 0 !important;
  }
  .calculator-container {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0.75rem !important;
    box-sizing: border-box !important;
  }
  .calculator-layout-modern {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    gap: 1rem !important;
  }
  .calculator-inputs-modern {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0.75rem !important;
    box-sizing: border-box !important;
  }
  .modern-inputs-grid {
    width: 100% !important;
    max-width: 100% !important;
    grid-template-columns: 1fr !important;
    gap: 0.75rem !important;
  }
  .calculator-inputs-modern .input-group {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0.5rem !important;
    margin: 0 0 0.5rem 0 !important;
    box-sizing: border-box !important;
  }
  .calculator-inputs-modern .modern-input {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0.5rem !important;
    font-size: 0.8rem !important;
    box-sizing: border-box !important;
    min-height: 40px !important;
  }
  .calculator-inputs-modern .input-label {
    font-size: 0.7rem !important;
    margin-bottom: 0.25rem !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
  }
}
@media (max-width: 360px) {
  .calculator-container {
    padding: 0.5rem !important;
  }
  .calculator-inputs-modern {
    padding: 0.5rem !important;
  }
  .calculator-inputs-modern .input-group {
    padding: 0.375rem !important;
  }
  .calculator-inputs-modern .modern-input {
    padding: 0.375rem !important;
    font-size: 0.75rem !important;
    min-height: 36px !important;
  }
  .calculator-inputs-modern .input-label {
    font-size: 0.65rem !important;
  }
  .modern-inputs-grid {
    gap: 0.5rem !important;
  }
}
@media (min-width: 600px) and (max-width: 710px) {
  .calculator-inputs-modern,
  .calculator-results-modern,
  .projection-section-modern {
    max-width: 100% !important;
    margin: 0 auto !important;
    padding: 1rem !important;
    width: 98% !important;
    box-sizing: border-box !important;
    overflow-x: hidden !important;
  }
  .calculator-inputs-modern .inputs-header p,
  .calculator-results-modern .results-header p,
  .projection-section-modern .projection-header p {
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    white-space: normal !important;
    -webkit-hyphens: auto !important;
            hyphens: auto !important;
    font-size: 0.9rem !important;
    line-height: 1.5 !important;
  }
  .calculator-inputs-modern *,
  .calculator-results-modern *,
  .projection-section-modern * {
    max-width: 100% !important;
    box-sizing: border-box !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
  }
  .calculator-inputs-modern .input-group {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0.875rem !important;
    margin: 0 0 0.875rem 0 !important;
  }
  .calculator-inputs-modern .modern-input,
  .calculator-inputs-modern select,
  .calculator-inputs-modern input {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0.75rem !important;
    font-size: 0.95rem !important;
    box-sizing: border-box !important;
  }
  .calculator-inputs-modern .input-label {
    font-size: 0.85rem !important;
    margin-bottom: 0.5rem !important;
    word-wrap: break-word !important;
    line-height: 1.4 !important;
  }
  .calculator-inputs-modern .modern-inputs-grid {
    grid-template-columns: 1fr !important;
    gap: 0.875rem !important;
    width: 100% !important;
  }
}
@media (min-width: 711px) and (max-width: 767px) {
  .calculator-inputs-modern,
  .calculator-results-modern,
  .projection-section-modern {
    max-width: 100% !important;
    margin: 0 auto !important;
    padding: 1.25rem !important;
    width: 96% !important;
    box-sizing: border-box !important;
    overflow-x: hidden !important;
  }
  .calculator-inputs-modern .inputs-header p,
  .calculator-results-modern .results-header p,
  .projection-section-modern .projection-header p {
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    white-space: normal !important;
    -webkit-hyphens: auto !important;
            hyphens: auto !important;
    font-size: 0.95rem !important;
    line-height: 1.5 !important;
    max-width: 100% !important;
  }
  .calculator-inputs-modern *,
  .calculator-results-modern *,
  .projection-section-modern * {
    max-width: 100% !important;
    box-sizing: border-box !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
  }
  .calculator-inputs-modern .input-group {
    width: 100% !important;
    max-width: 100% !important;
    padding: 1rem !important;
    margin: 0 0 1rem 0 !important;
  }
  .calculator-inputs-modern .modern-input,
  .calculator-inputs-modern select,
  .calculator-inputs-modern input {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0.875rem !important;
    font-size: 1rem !important;
    box-sizing: border-box !important;
  }
  .calculator-inputs-modern .input-label {
    font-size: 0.875rem !important;
    margin-bottom: 0.5rem !important;
    word-wrap: break-word !important;
    line-height: 1.4 !important;
  }
  .calculator-inputs-modern .modern-inputs-grid {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
    width: 100% !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .calculator-inputs-modern,
  .calculator-results-modern,
  .projection-section-modern {
    max-width: 100% !important;
    margin: 0 auto !important;
    padding: 1.5rem !important;
    width: 98% !important;
    box-sizing: border-box !important;
    overflow-x: hidden !important;
  }
  .calculator-inputs-modern .inputs-header p,
  .calculator-results-modern .results-header p,
  .projection-section-modern .projection-header p {
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    white-space: normal !important;
    font-size: 1rem !important;
    line-height: 1.5 !important;
    max-width: 100% !important;
  }
  .calculator-inputs-modern *,
  .calculator-results-modern *,
  .projection-section-modern * {
    max-width: 100% !important;
    box-sizing: border-box !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
  }
  .calculator-inputs-modern .input-group {
    width: 100% !important;
    max-width: 100% !important;
    padding: 1rem !important;
    margin: 0 0 1rem 0 !important;
    box-sizing: border-box !important;
  }
  .calculator-inputs-modern .modern-input,
  .calculator-inputs-modern select,
  .calculator-inputs-modern input {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0.875rem !important;
    font-size: 1rem !important;
    box-sizing: border-box !important;
    min-height: 44px !important;
  }
  .calculator-inputs-modern .input-label {
    font-size: 0.875rem !important;
    margin-bottom: 0.5rem !important;
    word-wrap: break-word !important;
    line-height: 1.4 !important;
    max-width: 100% !important;
  }
  .calculator-inputs-modern .modern-inputs-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
    gap: 1rem !important;
    width: 100% !important;
  }
  .calculator-results-modern .results-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
    gap: 1rem !important;
    width: 100% !important;
  }
  .calculator-results-modern .result-card {
    padding: 1rem !important;
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
  }
  .calculator-inputs-modern .inputs-header h3,
  .calculator-results-modern .results-header h3,
  .projection-section-modern .projection-header h3 {
    font-size: 1.5rem !important;
    line-height: 1.3 !important;
    margin-bottom: 0.75rem !important;
  }
}
@media (min-width: 481px) and (max-width: 599px) {
  .calculator-inputs-modern,
  .calculator-results-modern,
  .projection-section-modern {
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0.75rem !important;
    width: 100% !important;
    box-sizing: border-box !important;
    overflow-x: hidden !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
  }
  .calculator-inputs-modern *,
  .calculator-results-modern *,
  .projection-section-modern * {
    max-width: 100% !important;
    box-sizing: border-box !important;
  }
  .calculator-inputs-modern .input-group,
  .calculator-results-modern .result-card,
  .projection-section-modern .projection-card {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0.625rem !important;
    margin: 0 0 0.625rem 0 !important;
  }
  .calculator-inputs-modern .modern-input,
  .calculator-inputs-modern select,
  .calculator-inputs-modern input {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0.5rem !important;
    font-size: 0.8rem !important;
    box-sizing: border-box !important;
    min-height: 42px !important;
  }
  .calculator-inputs-modern .input-label {
    font-size: 0.7rem !important;
    margin-bottom: 0.3rem !important;
    word-wrap: break-word !important;
    line-height: 1.3 !important;
  }
  .calculator-inputs-modern .input-label,
  .calculator-inputs-modern .input-info,
  .calculator-inputs-modern .info-badge,
  .calculator-inputs-modern .info-text {
    white-space: normal !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    -webkit-hyphens: auto !important;
            hyphens: auto !important;
  }
  .calculator-inputs-modern button,
  .calculator-inputs-modern .btn {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    font-size: 0.8rem !important;
    padding: 0.6rem !important;
  }
  .calculator-inputs-modern .modern-inputs-grid {
    grid-template-columns: 1fr !important;
    gap: 0.625rem !important;
    width: 100% !important;
  }
  .calculator-inputs-modern .inputs-header h3,
  .calculator-results-modern .results-header h3,
  .projection-section-modern .projection-header h3 {
    font-size: 1.1rem !important;
    line-height: 1.3 !important;
  }
  .calculator-inputs-modern .inputs-header p,
  .calculator-results-modern .results-header p,
  .projection-section-modern .projection-header p {
    font-size: 0.8rem !important;
    line-height: 1.4 !important;
  }
}
@media (min-width: 361px) and (max-width: 480px) {
  .calculator-inputs-modern,
  .calculator-results-modern,
  .projection-section-modern {
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0.5rem !important;
    width: 100% !important;
    box-sizing: border-box !important;
  }
  .calculator-inputs-modern *,
  .calculator-results-modern *,
  .projection-section-modern * {
    max-width: 100% !important;
    box-sizing: border-box !important;
  }
  .calculator-inputs-modern .input-group,
  .calculator-results-modern .result-card,
  .projection-section-modern .projection-card {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0.5rem !important;
    margin: 0 0 0.5rem 0 !important;
  }
  .calculator-inputs-modern .modern-input,
  .calculator-inputs-modern select,
  .calculator-inputs-modern input {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0.4rem !important;
    font-size: 0.75rem !important;
    box-sizing: border-box !important;
  }
  .calculator-inputs-modern .input-label {
    font-size: 0.65rem !important;
    margin-bottom: 0.25rem !important;
    word-wrap: break-word !important;
  }
}
@media (min-width: 320px) and (max-width: 360px) {
  .calculator-inputs-modern,
  .calculator-results-modern,
  .projection-section-modern {
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0.4rem !important;
    width: 100% !important;
    box-sizing: border-box !important;
  }
  .calculator-inputs-modern *,
  .calculator-results-modern *,
  .projection-section-modern * {
    max-width: 100% !important;
    box-sizing: border-box !important;
  }
  .calculator-inputs-modern .input-group,
  .calculator-results-modern .result-card,
  .projection-section-modern .projection-card {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0.375rem !important;
    margin: 0 0 0.375rem 0 !important;
  }
  .calculator-inputs-modern .modern-input,
  .calculator-inputs-modern select,
  .calculator-inputs-modern input {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0.3rem !important;
    font-size: 0.7rem !important;
    box-sizing: border-box !important;
    min-height: 32px !important;
  }
  .calculator-inputs-modern .input-label {
    font-size: 0.6rem !important;
    margin-bottom: 0.2rem !important;
    word-wrap: break-word !important;
    line-height: 1.2 !important;
  }
}
@media (max-width: 480px) {
  .calculator-inputs-modern,
  .calculator-results-modern,
  .projection-section-modern {
    overflow-x: hidden !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
  }
  .calculator-inputs-modern .input-label,
  .calculator-inputs-modern .input-info,
  .calculator-inputs-modern .info-badge,
  .calculator-inputs-modern .info-text {
    white-space: normal !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    -webkit-hyphens: auto !important;
            hyphens: auto !important;
  }
  .calculator-inputs-modern button,
  .calculator-inputs-modern .btn {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    font-size: 0.75rem !important;
    padding: 0.5rem !important;
  }
  .calculator-inputs-modern .modern-inputs-grid {
    grid-template-columns: 1fr !important;
    gap: 0.5rem !important;
    width: 100% !important;
  }
}
@media (max-width: 319px) {
  .calculator-inputs-modern,
  .calculator-results-modern,
  .projection-section-modern {
    max-width: 100%;
    margin: 0;
    padding: 0.5rem;
    width: 100%;
    box-sizing: border-box;
  }
}
@media (min-width: 768px) {
  .calculator-layout-modern {
    gap: var(--spacing-xl);
  }
  .calculator-inputs-modern,
  .calculator-results-modern,
  .projection-section-modern {
    border-radius: 1rem;
  }
}
@media (max-width: 767px) {
  .calculator-layout-modern {
    gap: var(--spacing-md);
  }
  .calculator-inputs-modern,
  .calculator-results-modern,
  .projection-section-modern {
    border-radius: 0.75rem;
    margin-bottom: var(--spacing-md);
  }
}
@media (max-width: 480px) {
  .calculator-layout-modern {
    gap: var(--spacing-sm);
  }
  .calculator-inputs-modern,
  .calculator-results-modern,
  .projection-section-modern {
    border-radius: 0.5rem;
    margin-bottom: var(--spacing-sm);
  }
}
@media (max-width: 360px) {
  .calculator-layout-modern {
    gap: 0.75rem;
  }
  .calculator-inputs-modern,
  .calculator-results-modern,
  .projection-section-modern {
    border-radius: 0.375rem;
    margin-bottom: 0.75rem;
  }
}
.input-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}
.input-group.editable {
  background: rgba(251, 191, 36, 0.05);
  border: 1px solid rgba(251, 191, 36, 0.2);
  border-radius: 0.75rem;
  padding: var(--spacing-md);
}
.input-group.readonly {
  background: rgba(107, 114, 128, 0.1);
  border: 1px solid rgba(107, 114, 128, 0.2);
  border-radius: 0.75rem;
  padding: var(--spacing-md);
}
.input-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #fbbf24;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--spacing-xs);
  word-wrap: break-word;
  overflow-wrap: break-word;
  -webkit-hyphens: auto;
          hyphens: auto;
}
.modern-input {
  background: rgba(31, 41, 55, 0.8);
  border: 1px solid rgba(107, 114, 128, 0.4);
  border-radius: 0.5rem;
  padding: var(--spacing-sm) var(--spacing-md);
  color: #ffffff;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.2s ease;
  width: 100%;
  box-sizing: border-box;
  word-wrap: break-word;
  overflow-wrap: break-word;
}
.modern-input:focus {
  outline: none;
  border-color: #fbbf24;
  box-shadow: 0 0 0 3px rgba(251, 191, 36, 0.1);
}
.modern-input.readonly {
  background: rgba(107, 114, 128, 0.2);
  color: #9ca3af;
  cursor: not-allowed;
}
.input-info {
  margin-top: var(--spacing-xs);
}
.info-badge {
  display: inline-block;
  background: rgba(251, 191, 36, 0.2);
  color: #fbbf24;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
}
.info-text {
  color: #9ca3af;
  font-size: 0.75rem;
}
.gold-price-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-xs);
}
@media (max-width: 768px) {
  .gold-price-controls {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }
  .live-price-btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
    width: 100%;
    text-align: center;
  }
  .live-price-display {
    font-size: 0.7rem;
    align-self: center;
  }
}
@media (max-width: 480px) {
  .gold-price-controls {
    gap: var(--spacing-xs);
  }
  .live-price-btn {
    padding: 0.625rem;
    font-size: 0.75rem;
    border-radius: 0.5rem;
  }
  .live-price-display {
    font-size: 0.65rem;
  }
  .error-text {
    font-size: 0.65rem;
    text-align: center;
  }
}
.live-price-btn {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: #000000;
  border: none;
  border-radius: 0.375rem;
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}
.live-price-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  transform: translateY(-1px);
}
.live-price-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
.live-price-display {
  color: #9ca3af;
  font-size: 0.75rem;
}
.error-text {
  color: #ef4444;
  font-size: 0.75rem;
}
.calculator-results-modern {
  background: rgba(17, 24, 39, 0.6);
  border: 1px solid rgba(107, 114, 128, 0.3);
  border-radius: 1rem;
  padding: var(--spacing-xl);
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
}
.results-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}
.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-md);
}
@media (max-width: 768px) {
  .calculator-results-modern {
    padding: var(--spacing-lg);
    margin: 0 -var(--spacing-xs);
  }
  .results-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }
  .result-card {
    padding: var(--spacing-sm);
    gap: var(--spacing-sm);
  }
  .result-icon {
    font-size: 1.5rem;
  }
}
@media (max-width: 480px) {
  .calculator-results-modern {
    padding: var(--spacing-sm);
    margin: 0;
  }
  .results-header h3 {
    font-size: 1rem;
    line-height: 1.3;
  }
  .results-header p {
    font-size: 0.75rem;
    line-height: 1.4;
  }
  .result-card {
    padding: 0.75rem;
    gap: 0.5rem;
  }
  .result-icon {
    font-size: 1.25rem;
  }
  .result-label {
    font-size: 0.7rem;
  }
  .result-value {
    font-size: 0.9rem;
  }
}
@media (max-width: 360px) {
  .calculator-results-modern {
    padding: 0.75rem;
    margin: 0 -0.25rem;
  }
  .results-header h3 {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
  }
  .results-header p {
    font-size: 0.7rem;
    margin-bottom: 1rem;
  }
  .result-card {
    padding: 0.625rem;
    gap: 0.4rem;
  }
  .result-icon {
    font-size: 1.1rem;
  }
  .result-label {
    font-size: 0.65rem;
  }
  .result-value {
    font-size: 0.85rem;
  }
  .results-grid {
    gap: 0.5rem;
  }
}
@media (max-width: 480px) {
  .calculator-results-modern {
    padding: var(--spacing-md);
    margin: 0;
    border-radius: 0.75rem;
  }
  .results-header h3 {
    font-size: 1.125rem;
  }
  .results-header p {
    font-size: 0.8rem;
  }
  .result-card {
    padding: var(--spacing-xs);
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-xs);
  }
  .result-icon {
    font-size: 1.25rem;
  }
  .result-content {
    text-align: center;
  }
  .result-label {
    font-size: 0.75rem;
  }
  .result-value {
    font-size: 1rem;
  }
  .result-unit {
    font-size: 0.7rem;
  }
}
.result-card {
  background: rgba(31, 41, 55, 0.6);
  border: 1px solid rgba(107, 114, 128, 0.3);
  border-radius: 0.75rem;
  padding: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  transition: all 0.3s ease;
}
.result-card:hover {
  border-color: rgba(251, 191, 36, 0.5);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  transform: translateY(-2px);
}
.result-card.highlight {
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.1), rgba(251, 191, 36, 0.05));
  border-color: rgba(251, 191, 36, 0.4);
}
.result-card.border-gold {
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.15), rgba(251, 191, 36, 0.08));
  border: 2px solid rgba(251, 191, 36, 0.6);
  box-shadow: 0 0 20px rgba(251, 191, 36, 0.2);
}
.result-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  flex: 1;
}
.result-metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xs) 0;
}
.result-metric-label {
  font-size: 0.75rem;
  color: #9ca3af;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}
.result-metric-value {
  font-size: 0.875rem;
  color: #ffffff;
  font-weight: 600;
  letter-spacing: 0.05em;
}
.result-icon {
  font-size: 2rem;
  flex-shrink: 0;
}
.result-content {
  flex: 1;
}
.result-label {
  font-size: 0.75rem;
  color: #9ca3af;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  margin-bottom: var(--spacing-xs);
}
.result-value {
  font-size: 1.125rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: var(--spacing-xs);
  line-height: 1.2;
}
.result-unit {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
}
.projection-section-modern {
  background: rgba(17, 24, 39, 0.6);
  border: 1px solid rgba(107, 114, 128, 0.3);
  border-radius: 1rem;
  padding: var(--spacing-xl);
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
}
.projection-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}
@media (max-width: 768px) {
  .modern-inputs-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  .results-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }
  .result-card {
    padding: var(--spacing-sm);
  }
  .result-value {
    font-size: 1rem;
  }
  .calculator-inputs-modern,
  .calculator-results-modern,
  .projection-section-modern {
    padding: var(--spacing-md);
  }
}
@media (max-width: 480px) {
  .projection-section-modern {
    padding: var(--spacing-sm);
    margin: 0;
  }
  .projection-header h3 {
    font-size: 1rem;
    line-height: 1.3;
  }
  .projection-header p {
    font-size: 0.75rem;
    line-height: 1.4;
  }
}
@media (max-width: 360px) {
  .projection-section-modern {
    padding: 0.75rem;
    margin: 0 -0.25rem;
  }
  .projection-header h3 {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
  }
  .projection-header p {
    font-size: 0.7rem;
    margin-bottom: 1rem;
  }
}
.projection-tabs-container {
  width: 100%;
}
.tab-navigation {
  margin-bottom: var(--spacing-lg);
  border-bottom: 1px solid rgba(107, 114, 128, 0.3);
}
.tab-scroll-container {
  display: flex;
  gap: 2px;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  padding-bottom: 2px;
}
.tab-scroll-container::-webkit-scrollbar {
  display: none;
}
.tab-button {
  flex-shrink: 0;
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 0.875rem;
  font-weight: 600;
  border: none;
  border-radius: 0.5rem 0.5rem 0 0;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  position: relative;
}
.tab-active {
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.2), rgba(251, 191, 36, 0.1));
  color: #fbbf24;
  border-bottom: 2px solid #fbbf24;
}
.tab-inactive {
  background: rgba(31, 41, 55, 0.5);
  color: #9ca3af;
  border-bottom: 2px solid transparent;
}
.tab-inactive:hover {
  background: rgba(31, 41, 55, 0.8);
  color: #d1d5db;
}
.tab-content {
  padding: var(--spacing-lg);
  background: rgba(31, 41, 55, 0.3);
  border-radius: 0.75rem;
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
}
.year-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}
.projection-metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}
.metric-card {
  background: rgba(17, 24, 39, 0.6);
  border: 1px solid rgba(107, 114, 128, 0.3);
  border-radius: 0.5rem;
  padding: var(--spacing-md);
  text-align: center;
  transition: all 0.3s ease;
}
.metric-card:hover {
  border-color: rgba(251, 191, 36, 0.5);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  transform: translateY(-2px);
}
.metric-card.highlight {
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.1), rgba(251, 191, 36, 0.05));
  border-color: rgba(251, 191, 36, 0.4);
}
.metric-label {
  font-size: 0.75rem;
  color: #9ca3af;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--spacing-xs);
}
.metric-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: var(--spacing-xs);
  line-height: 1.2;
}
.metric-unit {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
}
@media (max-width: 768px) {
  .projection-metrics-grid {
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-sm);
  }
  .metric-card {
    padding: var(--spacing-sm);
  }
  .metric-value {
    font-size: 1rem;
  }
  .tab-button {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.75rem;
  }
}
@media (max-width: 480px) {
  .projection-metrics-grid {
    grid-template-columns: 1fr;
  }
}
.project-assumptions-section {
  margin-top: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: rgba(17, 24, 39, 0.4);
  border: 1px solid rgba(107, 114, 128, 0.2);
  border-radius: 0.75rem;
  -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);
}
.assumptions-header {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}
.assumptions-header h4 {
  color: #fbbf24;
  font-weight: 600;
  font-size: 1.125rem;
  margin-bottom: var(--spacing-xs);
}
.assumptions-header p {
  color: #9ca3af;
  font-size: 0.75rem;
  font-weight: 400;
}
.assumptions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}
.assumption-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  background: rgba(31, 41, 55, 0.3);
  border: 1px solid rgba(107, 114, 128, 0.2);
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}
.assumption-item:hover {
  border-color: rgba(251, 191, 36, 0.3);
  background: rgba(31, 41, 55, 0.5);
}
.assumption-label {
  font-size: 0.75rem;
  color: #d1d5db;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}
.assumption-value {
  font-size: 0.875rem;
  color: #ffffff;
  font-weight: 600;
  text-align: right;
}
@media (max-width: 768px) {
  .assumptions-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }
  .assumption-item {
    padding: var(--spacing-xs) var(--spacing-sm);
  }
  .assumption-label {
    font-size: 0.7rem;
  }
  .assumption-value {
    font-size: 0.8rem;
  }
  .project-assumptions-section {
    padding: var(--spacing-md);
    margin-top: var(--spacing-lg);
  }
}
@media (max-width: 480px) {
  .assumption-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }
  .assumption-value {
    text-align: left;
    color: #fbbf24;
  }
}
.gallery-container {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
}
.gallery-container .space-y-6 > * + * {
  margin-top: var(--spacing-xl);
}
.gallery-container img:not(.fixed img):not(.fixed * img) {
  max-width: 350px;
  width: 100%;
  height: auto;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}
.fixed img,
.fixed * img {
  max-width: calc(100vw - 100px) !important;
  max-height: calc(100vh - 200px) !important;
  width: auto !important;
  height: auto !important;
  -o-object-fit: contain !important;
     object-fit: contain !important;
}
.gallery-container img:hover {
  transform: scale(1.02);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 20px rgba(251, 191, 36, 0.1);
}
.gallery-container button {
  transition: all 0.3s ease;
  border-radius: 0.5rem;
}
.gallery-container button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}
.fixed.inset-0.z-50 img {
  max-width: none !important;
  width: auto !important;
  height: auto !important;
  min-width: unset !important;
  min-height: unset !important;
}
.gallery-lightbox-backdrop {
  -webkit-backdrop-filter: blur(12px);
          backdrop-filter: blur(12px);
  background: rgba(0, 0, 0, 0.95);
  transition: all 0.3s ease-in-out;
}
.gallery-lightbox-control {
  background: rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(245, 158, 11, 0.3);
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
  transition: all 0.2s ease;
}
.gallery-lightbox-control:hover {
  background: rgba(0, 0, 0, 0.8);
  border-color: rgba(245, 158, 11, 0.6);
  transform: scale(1.05);
}
@media (max-width: 768px) {
  .gallery-lightbox-control {
    min-height: 48px;
    min-width: 48px;
    padding: 12px;
  }
  .gallery-lightbox-nav {
    padding: 16px;
  }
}
.gallery-image-card {
  background: #1f2937;
  border: 1px solid #374151;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}
.gallery-image-card:hover {
  border-color: rgba(245, 158, 11, 0.4);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4), 0 0 20px rgba(245, 158, 11, 0.15);
}
.gallery-featured-badge {
  background: rgba(245, 158, 11, 0.2);
  border: 1px solid rgba(245, 158, 11, 0.3);
  color: #fbbf24;
  -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);
}
.gallery-category-badge {
  background: rgba(245, 158, 11, 0.2);
  border: 1px solid rgba(245, 158, 11, 0.3);
  color: #fbbf24;
  -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);
}
@media (max-width: 640px) {
  .gallery-container img {
    max-width: 100%;
  }
}
.gallery-container * {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
.fixed.inset-0.z-50 img,
.fixed.inset-0.z-50 .relative img {
  max-width: calc(100vw - 100px) !important;
  max-height: calc(100vh - 200px) !important;
  width: auto !important;
  height: auto !important;
  -o-object-fit: contain !important;
     object-fit: contain !important;
  min-width: unset !important;
  min-height: unset !important;
}
.lightbox-image {
  max-width: calc(100vw - 100px) !important;
  max-height: calc(100vh - 200px) !important;
  width: auto !important;
  height: auto !important;
  -o-object-fit: contain !important;
     object-fit: contain !important;
  display: block !important;
}
@media (min-width: 768px) {
  .lightbox-image {
    max-width: calc(100vw - 200px) !important;
    max-height: calc(100vh - 300px) !important;
  }
}
.site-values-tabs {
  background: rgba(15, 23, 42, 0.95);
  border-radius: 20px;
  padding: var(--spacing-xl);
  margin: var(--spacing-lg) 0;
  border: 1px solid rgba(51, 65, 85, 0.3);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
}
.site-values-tabs h3 {
  color: var(--gold);
  font-size: clamp(1.75rem, 3vw, 2.25rem);
  font-weight: 700;
  text-align: center;
  margin-bottom: var(--spacing-xs);
  letter-spacing: -0.02em;
}
.site-values-tabs .subtitle {
  color: #94a3b8;
  font-size: 1rem;
  text-align: center;
  margin-bottom: var(--spacing-xl);
  font-weight: 400;
}
.tabs-container {
  margin-bottom: var(--spacing-xl);
}
.tabs-nav {
  display: flex;
  gap: 4px;
  background: rgba(30, 41, 59, 0.6);
  padding: 6px;
  border-radius: 16px;
  border: 1px solid rgba(51, 65, 85, 0.4);
  flex-wrap: wrap;
  justify-content: center;
  max-width: 600px;
  margin: 0 auto;
}
.tab-button {
  background: transparent;
  border: none;
  color: #94a3b8;
  padding: 12px 16px;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  letter-spacing: 0.025em;
  flex: 1;
  min-width: 85px;
  text-align: center;
  white-space: nowrap;
}
.tab-button:hover {
  background: rgba(51, 65, 85, 0.5);
  color: #e2e8f0;
}
.tab-button.active {
  background: linear-gradient(135deg, var(--gold), #d97706);
  color: #1e293b;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(251, 191, 36, 0.3);
}
.tab-content {
  margin-top: var(--spacing-xl);
}
.site-values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}
.site-value-card {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.9), rgba(15, 23, 42, 0.95));
  border: 1px solid rgba(51, 65, 85, 0.4);
  border-radius: 16px;
  padding: var(--spacing-lg);
  transition: all 0.3s ease;
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}
.site-value-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--gold), #d97706);
  opacity: 0;
  transition: opacity 0.3s ease;
}
.site-value-card:hover {
  border-color: rgba(251, 191, 36, 0.5);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transform: translateY(-4px);
}
.site-value-card:hover::before {
  opacity: 1;
}
.site-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}
.site-title {
  color: #ffffff;
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
  letter-spacing: -0.01em;
}
.site-badge {
  background: rgba(251, 191, 36, 0.15);
  color: var(--gold);
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  letter-spacing: 0.05em;
  border: 1px solid rgba(251, 191, 36, 0.3);
}
.site-value {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}
.value-label {
  color: #94a3b8;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}
.value-amount {
  color: var(--gold);
  font-size: 1.5rem;
  font-weight: 700;
  letter-spacing: -0.02em;
}
.portfolio-summary {
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.1), rgba(251, 191, 36, 0.05));
  border: 2px solid rgba(251, 191, 36, 0.3);
  border-radius: 20px;
  padding: var(--spacing-xl);
  position: relative;
  overflow: hidden;
}
.portfolio-summary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--gold), #d97706, var(--gold));
}
.summary-header {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}
.summary-title {
  color: var(--gold);
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 var(--spacing-xs) 0;
  letter-spacing: -0.02em;
}
.summary-subtitle {
  color: #94a3b8;
  font-size: 0.875rem;
  font-weight: 500;
}
.summary-metrics {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-xl);
  flex-wrap: wrap;
}
.summary-metric {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
}
.summary-metric.primary .metric-value {
  color: var(--gold);
  font-size: 2rem;
  font-weight: 800;
}
.summary-metric.primary .metric-label {
  color: var(--gold);
  font-weight: 600;
}
.metric-value {
  color: #ffffff;
  font-size: 1.5rem;
  font-weight: 700;
  letter-spacing: -0.02em;
}
.metric-label {
  color: #94a3b8;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  text-align: center;
}
.summary-divider {
  width: 1px;
  height: 60px;
  background: linear-gradient(to bottom, transparent, rgba(251, 191, 36, 0.3), transparent);
}
.resource-estimates-modern {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95), rgba(30, 41, 59, 0.9));
  border-radius: 20px;
  padding: var(--spacing-xl);
  margin: var(--spacing-lg) 0;
  border: 1px solid rgba(51, 65, 85, 0.3);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}
.resource-estimates-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--gold), #d97706, var(--gold));
}
.resource-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}
.resource-title {
  color: var(--gold);
  font-size: clamp(1.5rem, 3vw, 2rem);
  font-weight: 700;
  margin: 0 0 var(--spacing-xs) 0;
  letter-spacing: -0.02em;
}
.resource-subtitle {
  color: #94a3b8;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}
.resource-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: var(--spacing-lg);
}
.resource-card {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8), rgba(15, 23, 42, 0.9));
  border: 1px solid rgba(51, 65, 85, 0.4);
  border-radius: 16px;
  padding: var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  transition: all 0.3s ease;
  -webkit-backdrop-filter: blur(5px);
          backdrop-filter: blur(5px);
  position: relative;
  overflow: hidden;
}
.resource-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, rgba(51, 65, 85, 0.5), rgba(71, 85, 105, 0.5));
  transition: all 0.3s ease;
}
.resource-card:hover {
  border-color: rgba(251, 191, 36, 0.4);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transform: translateY(-4px);
}
.resource-card:hover::before {
  background: linear-gradient(90deg, var(--gold), #d97706);
}
.resource-card.highlight {
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.15), rgba(251, 191, 36, 0.08));
  border: 2px solid rgba(251, 191, 36, 0.4);
  box-shadow: 0 4px 20px rgba(251, 191, 36, 0.2);
}
.resource-card.highlight::before {
  background: linear-gradient(90deg, var(--gold), #d97706, var(--gold));
  height: 3px;
}
.resource-icon {
  font-size: 2rem;
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(51, 65, 85, 0.3);
  border-radius: 12px;
  border: 1px solid rgba(71, 85, 105, 0.4);
}
.resource-card.highlight .resource-icon {
  background: rgba(251, 191, 36, 0.2);
  border-color: rgba(251, 191, 36, 0.4);
}
.resource-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}
.resource-label {
  color: #94a3b8;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}
.resource-value {
  color: #ffffff;
  font-size: 1.25rem;
  font-weight: 700;
  letter-spacing: -0.02em;
}
.resource-value.gold {
  color: var(--gold);
  font-size: 1.5rem;
  font-weight: 800;
}
.commission-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}
.commission-title {
  color: var(--gold);
  font-size: clamp(2rem, 4vw, 2.5rem);
  font-weight: 800;
  margin: 0 0 var(--spacing-md) 0;
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, var(--gold), #d97706);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
.commission-subtitle {
  color: #94a3b8;
  font-size: 1.125rem;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}
.commission-structure {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--spacing-xl);
  margin-top: var(--spacing-xl);
}
.commission-card {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95), rgba(30, 41, 59, 0.9));
  border: 1px solid rgba(51, 65, 85, 0.4);
  border-radius: 20px;
  padding: var(--spacing-xl);
  position: relative;
  overflow: hidden;
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  transition: all 0.4s ease;
}
.commission-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, rgba(51, 65, 85, 0.5), rgba(71, 85, 105, 0.5));
  transition: all 0.3s ease;
}
.commission-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
  border-color: rgba(251, 191, 36, 0.5);
}
.commission-card:hover::before {
  background: linear-gradient(90deg, var(--gold), #d97706, var(--gold));
}
.commission-card.direct-sales::before {
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}
.commission-card.example-card::before {
  background: linear-gradient(90deg, #10b981, #059669);
}
.commission-card.total-pool::before {
  background: linear-gradient(90deg, var(--gold), #d97706, var(--gold));
}
.commission-card-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}
.commission-icon {
  font-size: 2.5rem;
  width: 70px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(51, 65, 85, 0.3);
  border-radius: 16px;
  border: 1px solid rgba(71, 85, 105, 0.4);
  flex-shrink: 0;
}
.commission-card-title {
  color: var(--gold);
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0;
  letter-spacing: -0.01em;
}
.commission-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}
.commission-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  background: rgba(30, 41, 59, 0.5);
  border-radius: 12px;
  border: 1px solid rgba(51, 65, 85, 0.3);
}
.commission-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: #e2e8f0;
  font-weight: 500;
}
.commission-currency {
  font-size: 1.25rem;
}
.commission-value {
  color: var(--gold);
  font-size: 1.5rem;
  font-weight: 800;
  letter-spacing: -0.02em;
}
.commission-example {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-lg);
  padding: var(--spacing-lg) 0;
}
.example-reward {
  text-align: center;
  padding: var(--spacing-md);
  border-radius: 12px;
  border: 2px solid;
  min-width: 100px;
}
.example-reward.primary {
  background: rgba(16, 185, 129, 0.1);
  border-color: rgba(16, 185, 129, 0.4);
}
.example-reward.secondary {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.4);
}
.reward-amount {
  font-size: 1.5rem;
  font-weight: 800;
  color: #ffffff;
  margin-bottom: var(--spacing-xs);
}
.reward-type {
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}
.example-reward.primary .reward-type {
  color: #10b981;
}
.example-reward.secondary .reward-type {
  color: #3b82f6;
}
.example-plus {
  font-size: 2rem;
  font-weight: 800;
  color: var(--gold);
}
.pool-details {
  text-align: center;
}
.pool-amount {
  font-size: 3rem;
  font-weight: 900;
  color: var(--gold);
  margin-bottom: var(--spacing-sm);
  letter-spacing: -0.02em;
}
.pool-description {
  color: #94a3b8;
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: var(--spacing-lg);
}
.pool-visual {
  margin-top: var(--spacing-lg);
}
.pool-bar {
  width: 100%;
  height: 8px;
  background: rgba(51, 65, 85, 0.5);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}
.pool-fill {
  width: 45%;
  height: 100%;
  background: linear-gradient(90deg, var(--gold), #d97706);
  border-radius: 4px;
  position: relative;
  animation: poolFillAnimation 2s ease-out;
}
@keyframes poolFillAnimation {
  from {
    width: 0%;
  }
  to {
    width: 45%;
  }
}
@media (max-width: 768px) {
  .site-values-grid {
    grid-template-columns: 1fr;
  }
  .summary-metrics {
    flex-direction: column;
    gap: var(--spacing-lg);
  }
  .summary-divider {
    width: 60px;
    height: 1px;
    background: linear-gradient(to right, transparent, rgba(251, 191, 36, 0.3), transparent);
  }
  .tabs-nav {
    grid-template-columns: repeat(3, 1fr);
    gap: 4px;
  }
  .resource-grid {
    grid-template-columns: 1fr;
  }
  .resource-card {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-sm);
  }
  .resource-icon {
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
  }
  .commission-structure {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
  .commission-example {
    flex-direction: column;
    gap: var(--spacing-md);
  }
  .example-plus {
    transform: rotate(90deg);
  }
  .pool-amount {
    font-size: 2.5rem;
  }
}
@media (max-width: 1024px) {
  .projects-main-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
  }
  .projects-image-section {
    position: static;
    order: -1;
  }
  .humanitarian-image {
    max-height: 400px;
    -o-object-fit: cover;
       object-fit: cover;
  }
}
@media (max-width: 768px) {
  .charity-commitment {
    margin: var(--spacing-lg) var(--spacing-md);
    padding: var(--spacing-lg);
  }
  .commitment-amount {
    font-size: 2rem;
  }
  .commitment-text {
    font-size: 1rem;
  }
  .charity-subtitle {
    font-size: 1.125rem;
    padding: 0 var(--spacing-md);
  }
  .projects-main-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
  .projects-cards-section {
    gap: var(--spacing-lg);
  }
  .project-card {
    padding: var(--spacing-lg);
  }
  .project-card-header {
    flex-direction: row;
    gap: var(--spacing-md);
  }
  .project-icon {
    width: 50px;
    height: 50px;
  }
  .project-title {
    font-size: 1.5rem;
  }
  .intro-text {
    font-size: 1.125rem;
    padding: 0 var(--spacing-md);
  }
  .footer-title {
    font-size: 1.5rem;
  }
  .footer-text {
    font-size: 1rem;
    padding: 0 var(--spacing-md);
  }
}
@media (max-width: 480px) {
  .charity-title {
    font-size: 2rem;
  }
  .projects-main-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
  .project-card {
    padding: var(--spacing-md);
  }
  .projects-cards-section {
    gap: var(--spacing-md);
  }
  .project-feature {
    padding: var(--spacing-sm);
  }
}
@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false;
}
@property --tw-divide-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-gradient-position {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}
@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}
@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-leading {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-duration {
  syntax: "*";
  inherits: false;
}
@property --tw-content {
  syntax: "*";
  initial-value: "";
  inherits: false;
}
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *, ::before, ::after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-divide-y-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-content: "";
    }
  }
}

