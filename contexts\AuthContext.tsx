'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { supabase } from '@/lib/supabase-client'
import { dataSyncService } from '@/lib/data-sync'
import { ErrorLogger, AppError, ErrorType, ErrorSeverity } from '@/lib/error-handling'
import type { User } from '@supabase/supabase-js'

interface TelegramUser {
  id: number
  telegram_id: bigint
  username?: string
  first_name: string
  last_name?: string
  country?: string
  registration_mode: string
  referral_code?: string
  created_at: string
  updated_at: string
}

interface AuthUser extends User {
  telegram_profile?: TelegramUser
  hasAcceptedTerms?: boolean
  hasSelectedCountry?: boolean
  hasCompletedKYC?: boolean
}

interface AuthContextType {
  user: AuthUser | null
  loading: boolean
  signInWithTelegram: (telegramData: any) => Promise<{ success: boolean; error?: string }>
  signInWithEmail: (email: string, password: string) => Promise<{ success: boolean; error?: string }>
  signUp: (email: string, password: string, userData: any) => Promise<{ success: boolean; error?: string }>
  resendConfirmation: (email: string) => Promise<{ success: boolean; error?: string }>
  signOut: () => Promise<void>
  refreshUser: () => Promise<void>
  refreshSession: () => Promise<boolean>
  checkOnboardingStatus: () => Promise<{
    hasAcceptedTerms: boolean
    hasSelectedCountry: boolean
    hasCompletedKYC: boolean
  }>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [loading, setLoading] = useState(true)
  const [syncError, setSyncError] = useState<string | null>(null)

  // Test Supabase connection on mount
  useEffect(() => {
    const testConnection = async () => {
      try {
        console.log('🔍 Testing Supabase connection...')
        const { data, error } = await supabase.from('telegram_users').select('count').limit(1)
        if (error) {
          console.error('🔍 Supabase connection test failed:', error)
        } else {
          console.log('🔍 Supabase connection test successful:', data)
        }
      } catch (error) {
        console.error('🔍 Supabase connection test error:', error)
      }
    }
    testConnection()
  }, [])

  // Check onboarding status
  const checkOnboardingStatus = async () => {
    if (!user?.telegram_profile) {
      return {
        hasAcceptedTerms: false,
        hasSelectedCountry: false,
        hasCompletedKYC: false
      }
    }

    try {
      // Check terms acceptance
      const { data: termsData } = await supabase
        .from('terms_acceptance')
        .select('*')
        .eq('user_id', user.telegram_profile.id)
        .single()

      // Check country selection
      const hasSelectedCountry = !!user.telegram_profile.country

      // Check KYC status using correct table name
      const { data: kycData } = await supabase
        .from('kyc_information')
        .select('*')
        .eq('user_id', user.telegram_profile.id)
        .single()

      return {
        hasAcceptedTerms: !!termsData,
        hasSelectedCountry,
        hasCompletedKYC: !!kycData
      }
    } catch (error) {
      console.error('Error checking onboarding status:', error)
      return {
        hasAcceptedTerms: false,
        hasSelectedCountry: false,
        hasCompletedKYC: false
      }
    }
  }

  // Refresh user data
  const refreshUser = async () => {
    try {
      console.log('🔄 Refreshing user data...')
      const { data: { user: authUser } } = await supabase.auth.getUser()

      if (authUser) {
        console.log('🔄 User found:', authUser.email)

        // For development - simplified user setup
        try {
          // Get Telegram profile if exists
          const telegramId = authUser.user_metadata?.telegram_id
          if (telegramId) {
            console.log('🔄 Checking Telegram profile for ID:', telegramId)
            const { data: telegramProfile } = await supabase
              .from('telegram_users')
              .select('*')
              .eq('telegram_id', telegramId)
              .single()

            if (telegramProfile) {
              console.log('🔄 Telegram profile found')
              const onboardingStatus = await checkOnboardingStatus()
              setUser({
                ...authUser,
                telegram_profile: telegramProfile,
                ...onboardingStatus
              })
            } else {
              console.log('🔄 No Telegram profile, using basic user')
              setUser(authUser)
            }
          } else {
            console.log('🔄 No Telegram ID, using basic user')
            setUser(authUser)
          }
        } catch (profileError) {
          console.warn('🔄 Profile lookup failed, using basic user:', profileError)
          setUser(authUser)
        }
      } else {
        console.log('🔄 No user found')
        setUser(null)
      }
    } catch (error) {
      console.error('🔄 Error refreshing user:', error)
      setUser(null)
    }
  }

  // Sign in with Telegram
  const signInWithTelegram = async (telegramData: any) => {
    try {
      // Verify Telegram data (basic client-side check)
      if (!telegramData.id || !telegramData.first_name) {
        return { success: false, error: 'Invalid Telegram data' }
      }

      // Check if user exists in telegram_users table
      const { data: existingUser } = await supabase
        .from('telegram_users')
        .select('*')
        .eq('telegram_id', telegramData.id.toString())
        .single()

      if (existingUser) {
        // Create auth session for existing user
        const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
          email: `telegram_${telegramData.id}@aureus.africa`,
          password: `telegram_${telegramData.id}_auth`
        })

        if (authError) {
          // Try to create auth user if doesn't exist
          const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
            email: `telegram_${telegramData.id}@aureus.africa`,
            password: `telegram_${telegramData.id}_auth`,
            options: {
              data: {
                telegram_id: telegramData.id,
                telegram_username: telegramData.username,
                telegram_first_name: telegramData.first_name,
                telegram_last_name: telegramData.last_name
              }
            }
          })

          if (signUpError) {
            return { success: false, error: 'Authentication failed' }
          }
        }

        await refreshUser()
        return { success: true }
      } else {
        // New user - create in telegram_users table first
        const { data: newUser, error: createError } = await supabase
          .from('telegram_users')
          .insert({
            telegram_id: telegramData.id.toString(),
            username: telegramData.username || '',
            first_name: telegramData.first_name,
            last_name: telegramData.last_name || '',
            registration_mode: 'web'
          })
          .select()
          .single()

        if (createError) {
          return { success: false, error: 'Failed to create user profile' }
        }

        // Create auth user
        const { data: authData, error: authError } = await supabase.auth.signUp({
          email: `telegram_${telegramData.id}@aureus.africa`,
          password: `telegram_${telegramData.id}_auth`,
          options: {
            data: {
              telegram_id: telegramData.id,
              telegram_username: telegramData.username,
              telegram_first_name: telegramData.first_name,
              telegram_last_name: telegramData.last_name
            }
          }
        })

        if (authError) {
          return { success: false, error: 'Authentication setup failed' }
        }

        await refreshUser()
        return { success: true }
      }
    } catch (error) {
      console.error('Telegram sign in error:', error)
      return { success: false, error: 'Sign in failed' }
    }
  }

  // Sign in with email and password
  const signInWithEmail = async (email: string, password: string): Promise<{ success: boolean; error?: string }> => {
    try {
      console.log('🔐 Attempting to sign in with email:', email)

      const { data, error: authError } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      console.log('🔐 Sign in response:', { data, authError })

      if (authError) {
        console.error('🔐 Auth error:', authError)

        // Handle specific error cases
        if (authError.message.includes('Email not confirmed')) {
          return {
            success: false,
            error: 'DEVELOPMENT SETUP NEEDED: Email confirmation is enabled. Please disable it in Supabase dashboard: Authentication → Settings → Disable "Enable email confirmations"'
          }
        }

        if (authError.message.includes('Invalid login credentials')) {
          return {
            success: false,
            error: 'Invalid email or password. Please check your credentials and try again.'
          }
        }

        return { success: false, error: authError.message }
      }

      if (data.user) {
        console.log('🔐 Sign in successful, user:', data.user.email)
        await refreshUser()
        return { success: true }
      }

      return { success: false, error: 'Authentication failed' }
    } catch (error) {
      console.error('🔐 Email sign in error:', error)
      return { success: false, error: 'Sign in failed' }
    }
  }

  // Sign up with email and password - SIMPLIFIED VERSION
  const signUp = async (email: string, password: string, userData: any): Promise<{ success: boolean; error?: string }> => {
    console.log('🚀 SIMPLE SIGNUP START:', email)
    console.log('🚀 SUPABASE CLIENT STATUS:', !!supabase)
    console.log('🚀 USER DATA:', userData)

    try {
      console.log('🚀 Calling supabase.auth.signUp...')
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: { data: userData }
      })

      console.log('🚀 SIGNUP RESULT:', { data, error })

      if (error) {
        console.error('🚀 SIGNUP ERROR:', error.message)

        // Handle specific error cases
        if (error.message.includes('Email not confirmed')) {
          return {
            success: false,
            error: 'Please check your email and click the confirmation link before signing in.'
          }
        }

        if (error.message.includes('User already registered')) {
          return {
            success: false,
            error: 'An account with this email already exists. Please try signing in instead.'
          }
        }

        return { success: false, error: error.message }
      }

      if (data.user) {
        console.log('🚀 USER CREATED:', data.user.email)
        console.log('🚀 USER CONFIRMATION STATUS:', data.user.email_confirmed_at)

        // If email confirmation is required, show appropriate message
        if (!data.user.email_confirmed_at) {
          return {
            success: false,
            error: 'Registration successful! Please check your email and click the confirmation link to complete your account setup.'
          }
        }

        setUser(data.user)
        return { success: true }
      }

      return { success: false, error: 'No user returned from registration' }

    } catch (error) {
      console.error('🚀 SIGNUP EXCEPTION:', error)
      return { success: false, error: 'Registration failed due to network error' }
    }
  }

  // Resend email confirmation
  const resendConfirmation = async (email: string): Promise<{ success: boolean; error?: string }> => {
    try {
      console.log('📧 Resending confirmation email to:', email)

      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: email
      })

      if (error) {
        console.error('📧 Resend confirmation error:', error)
        return { success: false, error: error.message }
      }

      console.log('📧 Confirmation email resent successfully')
      return { success: true }
    } catch (error) {
      console.error('📧 Resend confirmation error:', error)
      return { success: false, error: 'Failed to resend confirmation email' }
    }
  }

  // Sign out
  const signOut = async () => {
    try {
      // Call logout API to clear cookies and sign out
      await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include'
      })

      // Clear local state
      setUser(null)

      // Redirect to login
      window.location.href = '/login'
    } catch (error) {
      console.error('Sign out error:', error)
      // Fallback: clear local state and redirect
      setUser(null)
      window.location.href = '/login'
    }
  }

  // Refresh session using API
  const refreshSession = async (): Promise<boolean> => {
    try {
      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        credentials: 'include'
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          await refreshUser()
          return true
        }
      }

      // If refresh failed, sign out
      setUser(null)
      return false
    } catch (error) {
      console.error('Session refresh error:', error)
      setUser(null)
      return false
    }
  }

  // Initialize auth state and real-time sync
  useEffect(() => {
    let userSyncUnsubscribe: (() => void) | null = null

    const initializeAuth = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession()

        if (session) {
          await refreshUser()
        }
      } catch (error) {
        console.error('Auth initialization error:', error)
        await ErrorLogger.logError(new AppError(
          `Auth initialization failed: ${error}`,
          ErrorType.AUTHENTICATION,
          ErrorSeverity.HIGH,
          { originalError: error }
        ))
      } finally {
        setLoading(false)
      }
    }

    const setupUserSync = (userId: number) => {
      try {
        // Subscribe to user-specific real-time updates
        userSyncUnsubscribe = dataSyncService.subscribeToUserUpdates(
          userId,
          async (payload) => {
            try {
              // Refresh user data when changes occur
              await refreshUser()
              setSyncError(null)
            } catch (error) {
              console.error('User sync error:', error)
              setSyncError('Failed to sync user data')

              await ErrorLogger.logError(new AppError(
                `User sync failed: ${error}`,
                ErrorType.SYSTEM,
                ErrorSeverity.MEDIUM,
                { userId, payload, originalError: error }
              ))
            }
          }
        )
      } catch (error) {
        console.error('Failed to setup user sync:', error)
        setSyncError('Failed to setup real-time synchronization')
      }
    }

    initializeAuth()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        try {
          if (event === 'SIGNED_IN' && session) {
            await refreshUser()

            // Setup real-time sync for the authenticated user
            const telegramId = session.user.user_metadata?.telegram_id
            if (telegramId) {
              const { data: telegramProfile } = await supabase
                .from('telegram_users')
                .select('id')
                .eq('telegram_id', telegramId)
                .single()

              if (telegramProfile) {
                setupUserSync(telegramProfile.id)
              }
            }
          } else if (event === 'SIGNED_OUT') {
            setUser(null)
            setSyncError(null)

            // Clean up sync subscription
            if (userSyncUnsubscribe) {
              userSyncUnsubscribe()
              userSyncUnsubscribe = null
            }
          }
        } catch (error) {
          console.error('Auth state change error:', error)
          await ErrorLogger.logError(new AppError(
            `Auth state change failed: ${error}`,
            ErrorType.AUTHENTICATION,
            ErrorSeverity.MEDIUM,
            { event, originalError: error }
          ))
        } finally {
          setLoading(false)
        }
      }
    )

    return () => {
      subscription.unsubscribe()
      if (userSyncUnsubscribe) {
        userSyncUnsubscribe()
      }
    }
  }, [])

  const value = {
    user,
    loading,
    signInWithTelegram,
    signInWithEmail,
    signUp,
    resendConfirmation,
    signOut,
    refreshUser,
    refreshSession,
    checkOnboardingStatus
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
