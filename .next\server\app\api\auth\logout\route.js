(()=>{var e={};e.id=716,e.ids=[716],e.modules={399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},8893:e=>{"use strict";e.exports=require("buffer")},4770:e=>{"use strict";e.exports=require("crypto")},7702:e=>{"use strict";e.exports=require("events")},2615:e=>{"use strict";e.exports=require("http")},8791:e=>{"use strict";e.exports=require("https")},8216:e=>{"use strict";e.exports=require("net")},8621:e=>{"use strict";e.exports=require("punycode")},6162:e=>{"use strict";e.exports=require("stream")},2452:e=>{"use strict";e.exports=require("tls")},7360:e=>{"use strict";e.exports=require("url")},1568:e=>{"use strict";e.exports=require("zlib")},8359:()=>{},3739:()=>{},8780:(e,t,r)=>{"use strict";r.r(t),r.d(t,{originalPathname:()=>g,patchFetch:()=>f,requestAsyncStorage:()=>x,routeModule:()=>l,serverHooks:()=>h,staticGenerationAsyncStorage:()=>d});var s={};r.r(s),r.d(s,{GET:()=>p,POST:()=>c});var o=r(9303),u=r(8716),i=r(670),a=r(7070),n=r(1066);async function c(e){try{e.cookies.get("sb-access-token")?.value&&await n.OQ.auth.signOut();let t=a.NextResponse.json({success:!0});return t.cookies.set("sb-access-token","",{path:"/",expires:new Date(0),sameSite:"lax",secure:!0}),t.cookies.set("sb-refresh-token","",{path:"/",expires:new Date(0),sameSite:"lax",secure:!0}),t}catch(e){return console.error("Logout error:",e),a.NextResponse.json({error:"Logout failed"},{status:500})}}async function p(e){try{let t=a.NextResponse.redirect(new URL("/login",e.url));return t.cookies.set("sb-access-token","",{path:"/",expires:new Date(0),sameSite:"lax",secure:!0}),t.cookies.set("sb-refresh-token","",{path:"/",expires:new Date(0),sameSite:"lax",secure:!0}),t}catch(t){return console.error("Logout redirect error:",t),a.NextResponse.redirect(new URL("/login?error=logout_failed",e.url))}}let l=new o.AppRouteRouteModule({definition:{kind:u.x.APP_ROUTE,page:"/api/auth/logout/route",pathname:"/api/auth/logout",filename:"route",bundlePath:"app/api/auth/logout/route"},resolvedPagePath:"C:\\xampp\\htdocs\\aureus_africa\\app\\api\\auth\\logout\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:x,staticGenerationAsyncStorage:d,serverHooks:h}=l,g="/api/auth/logout/route";function f(){return(0,i.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:d})}},1066:(e,t,r)=>{"use strict";r.d(t,{OQ:()=>i});var s=r(2438);let o=(void 0).VITE_SUPABASE_URL||"https://fgubaqoftdeefcakejwu.supabase.co",u=(void 0).VITE_SUPABASE_ANON_KEY;if(!u)throw Error("Missing VITE_SUPABASE_ANON_KEY environment variable");let i=(0,s.eI)(o,u,{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[276,169],()=>r(8780));module.exports=s})();