(()=>{var e={};e.id=547,e.ids=[547],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},8893:e=>{"use strict";e.exports=require("buffer")},4770:e=>{"use strict";e.exports=require("crypto")},7702:e=>{"use strict";e.exports=require("events")},2615:e=>{"use strict";e.exports=require("http")},8791:e=>{"use strict";e.exports=require("https")},8216:e=>{"use strict";e.exports=require("net")},8621:e=>{"use strict";e.exports=require("punycode")},6162:e=>{"use strict";e.exports=require("stream")},2452:e=>{"use strict";e.exports=require("tls")},7360:e=>{"use strict";e.exports=require("url")},1568:e=>{"use strict";e.exports=require("zlib")},9909:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d}),r(3160),r(3321),r(5866);var t=r(3191),a=r(8716),i=r(7922),n=r.n(i),l=r(5231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(s,o);let d=["",{children:["dashboard",{children:["referrals",{children:["convert",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,3160)),"C:\\xampp\\htdocs\\aureus_africa\\app\\dashboard\\referrals\\convert\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,3321)),"C:\\xampp\\htdocs\\aureus_africa\\app\\dashboard\\layout.tsx"]}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,5866,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\xampp\\htdocs\\aureus_africa\\app\\dashboard\\referrals\\convert\\page.tsx"],u="/dashboard/referrals/convert/page",m={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/dashboard/referrals/convert/page",pathname:"/dashboard/referrals/convert",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},2766:(e,s,r)=>{Promise.resolve().then(r.bind(r,8767))},8498:(e,s,r)=>{Promise.resolve().then(r.bind(r,5272))},8767:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n});var t=r(326),a=r(9240),i=r(668);function n({children:e}){let{user:s,signOut:r}=(0,a.a)(),n=async()=>{await r()};return t.jsx(i.Z,{children:(0,t.jsxs)("div",{className:"dashboard-layout min-h-screen bg-gray-50",children:[t.jsx("header",{className:"bg-gradient-to-r from-gray-900 via-blue-900 to-purple-900 text-white shadow-lg",children:t.jsx("div",{className:"container mx-auto px-4 py-4",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[t.jsx("h1",{className:"text-2xl font-bold text-yellow-400",children:"Aureus Alliance"}),t.jsx("span",{className:"text-sm text-gray-300",children:"Dashboard"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[s?.telegram_profile&&(0,t.jsxs)("div",{className:"text-sm",children:[t.jsx("span",{className:"text-gray-300",children:"Welcome, "}),t.jsx("span",{className:"text-yellow-400 font-medium",children:s.telegram_profile.first_name})]}),t.jsx("button",{onClick:n,className:"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 text-sm font-medium",children:"Logout"})]})]})})}),t.jsx("main",{className:"container mx-auto px-4 py-8",children:e})]})})}},5272:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>d});var t=r(326),a=r(7577),i=r(5047),n=r(9240),l=r(1579),o=r(668);function d(){let{user:e}=(0,n.a)(),s=(0,i.useRouter)(),[r,d]=(0,a.useState)(0),[c,u]=(0,a.useState)(null),[m,x]=(0,a.useState)(""),[h,p]=(0,a.useState)(0),[b,g]=(0,a.useState)(!1),[f,v]=(0,a.useState)(null),[j,y]=(0,a.useState)(!0),N=async t=>{if(t.preventDefault(),!e?.telegram_profile?.id||!c){v("User profile or phase information not found");return}let a=parseFloat(m);if(!a||a<=0){v("Please enter a valid conversion amount");return}if(a>r){v("Conversion amount exceeds available balance");return}if(0===h){v("Conversion amount too small to purchase shares");return}g(!0),v(null);try{let{error:r}=await l.OQ.from("commission_conversions").insert({user_id:e.telegram_profile.id,commission_amount:a,shares_purchased:h,share_price:c.price_per_share,phase_id:c.id,status:"pending"});if(r)throw Error(r.message);let{error:t}=await l.OQ.from("commission_transactions").insert({user_id:e.telegram_profile.id,transaction_type:"conversion",amount:-a,description:`Converted to ${h} shares at $${c.price_per_share}/share`,status:"pending"});t&&console.error("Error creating transaction record:",t),s.push("/dashboard/referrals?status=conversion_submitted")}catch(e){console.error("Conversion error:",e),v("Failed to submit conversion request. Please try again.")}finally{g(!1)}};return j?t.jsx(o.Z,{requireTerms:!0,requireCountry:!0,children:(0,t.jsxs)("div",{className:"animate-pulse",children:[t.jsx("div",{className:"h-8 bg-gray-200 rounded w-1/4 mb-4"}),t.jsx("div",{className:"h-64 bg-gray-200 rounded mb-4"})]})}):c?t.jsx(o.Z,{requireTerms:!0,requireCountry:!0,children:t.jsx("div",{className:"max-w-2xl mx-auto",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,t.jsxs)("div",{className:"mb-6",children:[t.jsx("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Convert Commission to Shares"}),t.jsx("p",{className:"text-gray-600",children:"Convert your USDT commission earnings into gold mining shares"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[(0,t.jsxs)("div",{className:"bg-green-50 rounded-lg p-4 border border-green-100",children:[t.jsx("h2",{className:"font-semibold text-green-800 mb-2",children:"Available Balance"}),(0,t.jsxs)("div",{className:"text-2xl font-bold text-green-600 mb-1",children:["$",r.toFixed(2)," USDT"]}),t.jsx("p",{className:"text-green-700 text-sm",children:"Available for conversion"})]}),(0,t.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4 border border-blue-100",children:[t.jsx("h2",{className:"font-semibold text-blue-800 mb-2",children:"Current Phase"}),(0,t.jsxs)("div",{className:"text-2xl font-bold text-blue-600 mb-1",children:["$",c.price_per_share.toFixed(2)]}),(0,t.jsxs)("p",{className:"text-blue-700 text-sm",children:["Price per share (Phase #",c.phase_number,")"]})]})]}),0===r?(0,t.jsxs)("div",{className:"bg-yellow-50 rounded-lg p-4 mb-6 border border-yellow-200",children:[t.jsx("h3",{className:"font-medium text-yellow-800 mb-2",children:"No Commission Available"}),t.jsx("p",{className:"text-yellow-700 text-sm",children:"You don't have any commission balance available for conversion. Start referring users to earn commissions!"}),t.jsx("div",{className:"mt-3",children:t.jsx("a",{href:"/dashboard/referrals",className:"text-yellow-800 hover:text-yellow-900 font-medium text-sm underline",children:"← Back to Referrals Dashboard"})})]}):(0,t.jsxs)("form",{onSubmit:N,className:"space-y-6",children:[(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-gray-700 font-medium mb-2",children:"Conversion Amount (USDT) *"}),(0,t.jsxs)("div",{className:"relative",children:[t.jsx("input",{type:"number",value:m,onChange:e=>x(e.target.value),placeholder:"Enter amount to convert",min:"0.01",max:r,step:"0.01",required:!0,className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 pr-16"}),t.jsx("button",{type:"button",onClick:()=>{x(r.toString())},className:"absolute right-2 top-1/2 transform -translate-y-1/2 bg-blue-100 hover:bg-blue-200 text-blue-700 px-3 py-1 rounded text-sm font-medium transition-colors duration-200",children:"MAX"})]}),(0,t.jsxs)("p",{className:"text-gray-500 text-sm mt-1",children:["Maximum: $",r.toFixed(2)," USDT"]})]}),h>0&&(0,t.jsxs)("div",{className:"bg-purple-50 rounded-lg p-4 border border-purple-100",children:[t.jsx("h3",{className:"font-medium text-purple-800 mb-3",children:"Conversion Preview"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm text-purple-700",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{children:"Commission Amount:"}),(0,t.jsxs)("span",{className:"font-medium",children:["$",parseFloat(m).toFixed(2)," USDT"]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{children:"Share Price:"}),(0,t.jsxs)("span",{className:"font-medium",children:["$",c.price_per_share.toFixed(2)]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{children:"Phase:"}),(0,t.jsxs)("span",{className:"font-medium",children:["#",c.phase_number]})]}),t.jsx("hr",{className:"border-purple-200"}),(0,t.jsxs)("div",{className:"flex justify-between font-semibold",children:[t.jsx("span",{children:"Shares to Receive:"}),t.jsx("span",{className:"text-purple-800",children:h.toLocaleString()})]}),(0,t.jsxs)("div",{className:"flex justify-between text-xs",children:[t.jsx("span",{children:"Exact Value:"}),(0,t.jsxs)("span",{children:["$",(h*c.price_per_share).toFixed(2)]})]})]})]}),f&&t.jsx("div",{className:"p-3 bg-red-100 text-red-700 rounded-md text-sm",children:f}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("button",{type:"button",onClick:()=>s.push("/dashboard/referrals"),className:"px-6 py-2 bg-gray-300 hover:bg-gray-400 text-gray-800 rounded-lg transition-colors duration-200",children:"Cancel"}),t.jsx("button",{type:"submit",disabled:b||!m||0===h,className:`px-6 py-2 rounded-lg transition-colors duration-200 ${b||!m||0===h?"bg-blue-300 text-white cursor-not-allowed":"bg-blue-600 hover:bg-blue-700 text-white"}`,children:b?"Processing...":"Convert to Shares"})]})]}),(0,t.jsxs)("div",{className:"mt-8 bg-gray-50 rounded-lg p-4",children:[t.jsx("h3",{className:"font-medium text-gray-800 mb-2",children:"Benefits of Converting to Shares"}),(0,t.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1 list-disc list-inside",children:[t.jsx("li",{children:"Earn dividends from gold mining operations"}),t.jsx("li",{children:"Own physical assets in gold mining equipment and land"}),t.jsx("li",{children:"Benefit from potential share price appreciation"}),t.jsx("li",{children:"Receive digital share certificates"}),t.jsx("li",{children:"Participate in company growth and expansion"}),t.jsx("li",{children:"No withdrawal fees or network costs"})]})]}),(0,t.jsxs)("div",{className:"mt-4 bg-blue-50 rounded-lg p-4",children:[t.jsx("h3",{className:"font-medium text-blue-800 mb-2",children:"Important Notes"}),(0,t.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1 list-disc list-inside",children:[t.jsx("li",{children:"Conversions are processed at current phase prices"}),t.jsx("li",{children:"All conversions require admin approval"}),t.jsx("li",{children:"Shares are allocated after approval (usually within 24 hours)"}),t.jsx("li",{children:"Fractional shares are rounded down to whole numbers"}),t.jsx("li",{children:"You'll receive a notification once conversion is complete"})]})]})]})})}):t.jsx(o.Z,{requireTerms:!0,requireCountry:!0,children:t.jsx("div",{className:"max-w-2xl mx-auto",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 text-center",children:[t.jsx("h1",{className:"text-2xl font-bold text-gray-800 mb-4",children:"No Active Phase"}),t.jsx("p",{className:"text-gray-600 mb-4",children:"There is currently no active investment phase available for conversion."}),t.jsx("a",{href:"/dashboard/referrals",className:"text-blue-600 hover:text-blue-800 font-medium underline",children:"← Back to Referrals Dashboard"})]})})})}},3321:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(8570).createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\app\dashboard\layout.tsx#default`)},3160:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(8570).createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\app\dashboard\referrals\convert\page.tsx#default`)}};var s=require("../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[276,471,529,585],()=>r(9909));module.exports=t})();