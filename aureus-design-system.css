/* ===== PROFESSIONAL AUREUS DESIGN SYSTEM ===== */

:root {
  /* Professional Color Palette */
  --primary-gold: #d4af37;
  --secondary-gold: #b8860b;
  --accent-blue: #1e40af;
  --text-primary: #ffffff;
  --text-secondary: #e5e7eb;
  --text-muted: #9ca3af;
  --background-dark: #000000;
  --background-card: rgba(0, 0, 0, 0.4);
  --border-subtle: rgba(255, 255, 255, 0.15);

  /* Responsive Professional Spacing - Mobile First */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 0.75rem;
  --spacing-lg: 1rem;
  --spacing-xl: 1.25rem;
  --spacing-2xl: 1.5rem;
  --spacing-3xl: 2rem;

  /* Typography */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;

  /* Tight Layout */
  --container-max-width: 1400px;
  --section-padding: var(--spacing-xl);
  --card-padding: var(--spacing-md);

  /* Responsive Breakpoints */
  --breakpoint-mobile: 320px;
  --breakpoint-mobile-lg: 480px;
  --breakpoint-tablet: 768px;
  --breakpoint-tablet-lg: 1024px;
  --breakpoint-desktop: 1200px;
  --breakpoint-desktop-lg: 1400px;
  --breakpoint-desktop-xl: 1920px;
}

/* ===== PROFESSIONAL BASE STYLES ===== */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: var(--background-dark);
  color: var(--text-primary);
  line-height: 1.6;
  font-size: clamp(1rem, 1.5vw, 1.125rem);
  letter-spacing: 2px;
}

/* Mobile-first typography scaling */
@media (min-width: 480px) {
  body {
    font-size: clamp(1.125rem, 1.75vw, 1.25rem);
    letter-spacing: 2px;
  }
}

@media (min-width: 768px) {
  body {
    line-height: 1.7;
    font-size: clamp(1.125rem, 2vw, 1.25rem);
    letter-spacing: 2px;
  }

  :root {
    --spacing-xl: 1.5rem;
    --spacing-2xl: 2rem;
    --spacing-3xl: 2.5rem;
  }
}

@media (min-width: 1024px) {
  :root {
    --spacing-xl: 2rem;
    --spacing-2xl: 2.5rem;
    --spacing-3xl: 3rem;
  }
}

@media (min-width: 1200px) {
  :root {
    --spacing-xl: 2.5rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
  }
}

/* ===== PROFESSIONAL LAYOUT SYSTEM ===== */

/* Clean Container System - Mobile First */
.container {
  width: 100%;
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--spacing-sm); /* Start with smaller mobile padding */
}

@media (min-width: 480px) {
  .container {
    padding: 0 var(--spacing-md);
  }
}

@media (min-width: 768px) {
  .container {
    padding: 0 var(--spacing-lg);
  }
}

@media (min-width: 1200px) {
  .container {
    padding: 0 var(--spacing-xl);
  }
}

/* Additional Responsive Container Classes */
.container-sm {
  width: 100%;
  max-width: 640px;
  margin: 0 auto;
  padding: 0 var(--spacing-sm);
}

.container-md {
  width: 100%;
  max-width: 768px;
  margin: 0 auto;
  padding: 0 var(--spacing-sm);
}

.container-lg {
  width: 100%;
  max-width: 1024px;
  margin: 0 auto;
  padding: 0 var(--spacing-sm);
}

.container-xl {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--spacing-sm);
}

.container-fluid {
  width: 100%;
  padding: 0 var(--spacing-sm);
}

@media (min-width: 480px) {
  .container-sm,
  .container-md,
  .container-lg,
  .container-xl,
  .container-fluid {
    padding: 0 var(--spacing-md);
  }
}

@media (min-width: 768px) {
  .container-sm,
  .container-md,
  .container-lg,
  .container-xl,
  .container-fluid {
    padding: 0 var(--spacing-lg);
  }
}

@media (min-width: 1200px) {
  .container-sm,
  .container-md,
  .container-lg,
  .container-xl,
  .container-fluid {
    padding: 0 var(--spacing-xl);
  }
}

/* Mobile-First Section Spacing */
.section {
  padding: var(--spacing-md) 0; /* Smaller mobile spacing */
}

.section-hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  padding: var(--spacing-lg) 0; /* Smaller mobile hero padding */
}

.section-compact {
  padding: var(--spacing-sm) 0;
}

@media (min-width: 768px) {
  .section {
    padding: var(--spacing-lg) 0;
  }

  .section-hero {
    padding: var(--spacing-xl) 0;
  }

  .section-compact {
    padding: var(--spacing-md) 0;
  }
}

@media (min-width: 1024px) {
  .section {
    padding: var(--spacing-xl) 0;
  }

  .section-hero {
    padding: var(--spacing-2xl) 0;
  }
}

/* Tight Grid System */
.grid {
  display: grid;
  gap: var(--spacing-md);
  width: 100%;
}

.grid-1 { grid-template-columns: 1fr; }
.grid-2 { grid-template-columns: repeat(2, 1fr); }
.grid-3 { grid-template-columns: repeat(3, 1fr); }
.grid-4 { grid-template-columns: repeat(4, 1fr); }

/* Mobile-first responsive grid system */
@media (max-width: 767px) {
  .grid-2,
  .grid-3,
  .grid-4 {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .grid-3,
  .grid-4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Professional Flexbox Utilities */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-sm { gap: var(--spacing-sm); }
.gap-md { gap: var(--spacing-md); }
.gap-lg { gap: var(--spacing-lg); }

/* ===== ENHANCED PROFESSIONAL TYPOGRAPHY SYSTEM ===== */

.heading-hero {
    font-size: clamp(1.0rem, 6vw, 3rem);
}

.heading-xl {
  font-size: clamp(2rem, 6vw, 3rem);
  font-weight: 600;
  line-height: 1.2;
  letter-spacing: 2px;
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.heading-lg {
  font-size: clamp(1.75rem, 5vw, 2.5rem);
  font-weight: 600;
  line-height: 1.3;
  letter-spacing: 2px;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.heading-md {
  font-size: clamp(1.5rem, 4vw, 2rem);
  font-weight: 500;
  line-height: 1.4;
  letter-spacing: 2px;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.heading-sm {
  font-size: clamp(1.25rem, 3vw, 1.75rem);
  font-weight: 500;
  line-height: 1.4;
  letter-spacing: 2px;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.heading-xs {
  font-size: clamp(1.125rem, 2.5vw, 1.5rem);
  font-weight: 500;
  line-height: 1.5;
  letter-spacing: 2px;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.text-lg {
  font-size: clamp(1.25rem, 2.5vw, 1.5rem);
  line-height: 1.6;
  letter-spacing: 2px;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}

.text-base {
  font-size: clamp(1.125rem, 2vw, 1.25rem);
  line-height: 1.6;
  letter-spacing: 2px;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}

.text-sm {
  font-size: clamp(1rem, 1.5vw, 1.125rem);
  line-height: 1.5;
  letter-spacing: 2px;
  color: var(--text-muted);
  margin-bottom: var(--spacing-xs);
}

/* Additional Typography Sizes for Enhanced Hierarchy */
.text-xl {
  font-size: clamp(1.5rem, 3vw, 1.75rem);
  line-height: 1.5;
  letter-spacing: 2px;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}

.text-xs {
  font-size: clamp(0.875rem, 1.25vw, 1rem);
  line-height: 1.4;
  letter-spacing: 2px;
  color: var(--text-muted);
  margin-bottom: var(--spacing-xs);
}

/* Professional Typography Variants */
.text-description {
  font-size: clamp(1.125rem, 2vw, 1.25rem);
  line-height: 1.6;
  letter-spacing: 2px;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}

.text-label {
  font-size: clamp(1rem, 1.5vw, 1.125rem);
  line-height: 1.4;
  letter-spacing: 2px;
  color: var(--text-muted);
  font-weight: 500;
  text-transform: uppercase;
}

.text-feature {
  font-size: clamp(1rem, 1.75vw, 1.125rem);
  line-height: 1.5;
  letter-spacing: 2px;
  color: var(--text-secondary);
}

/* Professional Color Classes */
.text-gold {
  color: var(--primary-gold);
}

.text-blue {
  color: var(--accent-blue);
}

/* ===== PROFESSIONAL COMPONENTS ===== */

/* Responsive Card System - Mobile First */
.card {
  background: var(--background-card);
  border-radius: 0.5rem;
  padding: var(--spacing-sm);
  transition: all 0.3s ease;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.card:hover {
  background: rgba(0, 0, 0, 0.6);
}

.card-compact {
  padding: var(--spacing-xs);
}

.card-hero {
  padding: var(--spacing-md);
}

/* Card responsive padding */
@media (min-width: 480px) {
  .card {
    padding: var(--spacing-md);
  }

  .card-compact {
    padding: var(--spacing-sm);
  }

  .card-hero {
    padding: var(--spacing-lg);
  }
}

@media (min-width: 768px) {
  .card {
    padding: var(--card-padding);
  }

  .card-hero {
    padding: var(--spacing-xl);
  }
}

@media (min-width: 1024px) {
  .card-hero {
    padding: var(--spacing-2xl);
  }
}

/* Card layout utilities */
.card-grid {
  display: grid;
  gap: var(--spacing-md);
  grid-template-columns: 1fr;
}

@media (min-width: 768px) {
  .card-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
  }
}

@media (min-width: 1024px) {
  .card-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-xl);
  }
}

.card-flex {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

@media (min-width: 768px) {
  .card-flex {
    flex-direction: row;
    gap: var(--spacing-md);
  }
}

/* Glass Card System for Admin */
.glass-card {
  background: rgba(0, 0, 0, 0.85);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 1.5rem;
  padding: 2.5rem;
  transition: all 0.3s ease;
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.glass-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.5), transparent);
}

.glass-card:hover {
  background: rgba(0, 0, 0, 0.9);
  border-color: rgba(212, 175, 55, 0.3);
  box-shadow:
    0 25px 80px rgba(0, 0, 0, 0.6),
    0 0 0 1px rgba(212, 175, 55, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

.glass-card-strong {
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(25px);
  border: 1px solid rgba(212, 175, 55, 0.4);
  box-shadow:
    0 30px 100px rgba(0, 0, 0, 0.7),
    0 0 0 1px rgba(212, 175, 55, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Enhanced Professional Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: 0.5rem;
  font-weight: 500;
  font-size: clamp(1rem, 1.5vw, 1.125rem);
  letter-spacing: 2px;
  text-decoration: none;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  white-space: nowrap;
  min-height: 44px; /* Touch-friendly minimum */
  min-width: 44px;
  box-sizing: border-box;
  text-transform: uppercase;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-gold), var(--secondary-gold));
  color: var(--background-dark);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(212, 175, 55, 0.3);
}

.btn-secondary {
  background: transparent;
  color: var(--text-primary);
  border: 1px solid var(--border-subtle);
}

.btn-secondary:hover {
  background: var(--background-card);
  border-color: var(--primary-gold);
}

.btn-outline {
  background: transparent;
  color: var(--primary-gold);
  border: 1px solid var(--primary-gold);
}

.btn-outline:hover {
  background: var(--primary-gold);
  color: var(--background-dark);
  border-color: var(--primary-gold);
}

/* Button size variants */
.btn-sm {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: clamp(0.875rem, 1.25vw, 1rem);
  letter-spacing: 2px;
  min-height: 36px;
}

.btn-lg {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: clamp(1.125rem, 1.75vw, 1.25rem);
  letter-spacing: 2px;
  min-height: 52px;
}

/* Mobile responsive button adjustments */
@media (max-width: 767px) {
  .btn {
    min-height: 48px; /* Larger touch targets on mobile */
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: clamp(1rem, 2vw, 1.125rem);
    letter-spacing: 2px;
  }

  .btn-sm {
    min-height: 40px;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: clamp(0.875rem, 1.5vw, 1rem);
    letter-spacing: 2px;
  }

  .btn-lg {
    min-height: 56px;
    padding: var(--spacing-lg) var(--spacing-xl);
    font-size: clamp(1.125rem, 2.5vw, 1.25rem);
    letter-spacing: 2px;
  }
}

/* Button layout utilities */
.btn-block {
  width: 100%;
  display: flex;
}

.btn-group {
  display: flex;
  gap: var(--spacing-sm);
}

@media (max-width: 767px) {
  .btn-group {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .btn-group .btn {
    width: 100%;
  }
}

/* Professional Spacing Utilities */
.mb-xs { margin-bottom: var(--spacing-xs); }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }

.mt-xs { margin-top: var(--spacing-xs); }
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }

.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }

/* Override Tailwind Conflicts - Clean Approach */
.container,
.max-w-7xl,
.max-w-6xl,
.max-w-5xl,
.max-w-4xl,
.max-w-3xl {
  width: 100% !important;
  max-width: var(--container-max-width) !important;
  margin: 0 auto !important;
  padding: 0 var(--spacing-md) !important;
}

@media (min-width: 768px) {
  .container,
  .max-w-7xl,
  .max-w-6xl,
  .max-w-5xl,
  .max-w-4xl,
  .max-w-3xl {
    padding: 0 var(--spacing-lg) !important;
  }
}

/* Professional Gradient Text Utilities */
.text-gradient-gold {
  background: linear-gradient(135deg, var(--primary-gold), var(--secondary-gold));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
}

.text-gradient-cyber {
  background: linear-gradient(135deg, var(--accent-blue), var(--primary-gold));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
}

/* Clean Section Spacing */
.py-24, .py-32, .py-20, .py-16 {
  padding-top: var(--section-padding) !important;
  padding-bottom: var(--section-padding) !important;
}

/* Remove Excessive Margins */
.mb-20, .mb-16, .mb-12, .mb-8, .mb-6, .mb-4 {
  margin-bottom: var(--spacing-sm) !important;
}

/* Clean Gaps */
.gap-12, .gap-16, .gap-8, .gap-6, .gap-4 {
  gap: var(--spacing-md) !important;
}

/* Remove Overlapping */
.-mt-8, .-mt-12, .-mt-16 {
  margin-top: 0 !important;
}

/* ===== FOOTER STYLES ===== */
.footer-container {
  background: rgba(0, 0, 0, 0.95);
  padding: var(--spacing-2xl) 0;
  border-top: 1px solid var(--border-subtle);
}

.footer-content {
  text-align: center;
}

.footer-logo {
  font-size: clamp(2rem, 4vw, 2.5rem);
  font-weight: 700;
  color: var(--primary-gold);
  margin-bottom: var(--spacing-lg);
  letter-spacing: 3px;
}

.footer-nav {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.footer-legal-nav {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
}

.footer-legal-nav a {
  color: var(--text-muted);
  font-size: var(--font-size-sm);
  text-decoration: none;
  transition: color 0.3s ease;
  letter-spacing: 1px;
}

.footer-legal-nav a:hover {
  color: var(--primary-gold);
}

.footer-cta {
  margin-bottom: var(--spacing-xl);
}

.footer-legal {
  color: var(--text-muted);
  font-size: var(--font-size-sm);
  line-height: 1.6;
}

.footer-legal p {
  margin-bottom: var(--spacing-sm);
}

/* Mobile responsive footer */
@media (max-width: 767px) {
  .footer-nav {
    gap: var(--spacing-md);
  }

  .footer-legal-nav {
    gap: var(--spacing-sm);
    flex-direction: column;
    align-items: center;
  }
}

/* ===== LEGAL SECTIONS ===== */
.legal-section {
  min-height: 100vh;
  padding: var(--spacing-3xl) 0;
  background: var(--background-dark);
  color: var(--text-primary);
}

.legal-content {
  max-width: 800px;
  margin: 0 auto;
  padding: var(--spacing-xl);
}

.back-button {
  background: transparent;
  border: 1px solid var(--primary-gold);
  color: var(--primary-gold);
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: 4px;
  cursor: pointer;
  margin-bottom: var(--spacing-xl);
  transition: all 0.3s ease;
  font-size: var(--font-size-sm);
}

.back-button:hover {
  background: var(--primary-gold);
  color: var(--background-dark);
}

.legal-text {
  line-height: 1.6;
}

.legal-text h2 {
  color: var(--primary-gold);
  font-size: var(--font-size-xl);
  margin: var(--spacing-xl) 0 var(--spacing-md) 0;
  font-weight: 600;
}

.legal-text p {
  margin-bottom: var(--spacing-md);
  color: var(--text-secondary);
}

.legal-links {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  flex-wrap: wrap;
}

.legal-links a {
  color: var(--primary-gold);
  text-decoration: none;
  font-size: var(--font-size-sm);
  transition: color 0.3s ease;
}

.legal-links a:hover {
  color: var(--secondary-gold);
  text-decoration: underline;
}

@media (max-width: 768px) {
  .legal-content {
    padding: var(--spacing-md);
  }

  .legal-links {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
}
