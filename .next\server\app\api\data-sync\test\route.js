(()=>{var e={};e.id=461,e.ids=[461],e.modules={399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},8893:e=>{"use strict";e.exports=require("buffer")},4770:e=>{"use strict";e.exports=require("crypto")},7702:e=>{"use strict";e.exports=require("events")},2615:e=>{"use strict";e.exports=require("http")},8791:e=>{"use strict";e.exports=require("https")},8216:e=>{"use strict";e.exports=require("net")},8621:e=>{"use strict";e.exports=require("punycode")},6162:e=>{"use strict";e.exports=require("stream")},2452:e=>{"use strict";e.exports=require("tls")},7360:e=>{"use strict";e.exports=require("url")},1568:e=>{"use strict";e.exports=require("zlib")},8359:()=>{},3739:()=>{},7334:(e,t,s)=>{"use strict";s.r(t),s.d(t,{originalPathname:()=>m,patchFetch:()=>g,requestAsyncStorage:()=>d,routeModule:()=>p,serverHooks:()=>h,staticGenerationAsyncStorage:()=>l});var r={};s.r(r),s.d(r,{POST:()=>u});var a=s(9303),i=s(8716),n=s(670),o=s(7070),c=s(1066);async function u(e){try{let t=e.cookies.get("sb-access-token")?.value;if(!t)return o.NextResponse.json({error:"No access token provided"},{status:401});let{data:{user:s},error:r}=await c.OQ.auth.getUser(t);if(r||!s)return o.NextResponse.json({error:"Invalid or expired token"},{status:401});let{telegram_id:a,test_timestamp:i,test_data:n}=await e.json();if(!a)return o.NextResponse.json({error:"Telegram ID is required"},{status:400});let{data:u,error:p}=await c.OQ.from("telegram_users").select("*").eq("telegram_id",a).single();if(p||!u)return o.NextResponse.json({error:"Telegram user not found"},{status:404});let d=[];try{let{error:e}=await c.OQ.from("telegram_users").update({last_sync_test:i,sync_test_data:n,updated_at:new Date().toISOString()}).eq("id",u.id);e?d.push({test:"Profile Update",success:!1,error:e.message}):d.push({test:"Profile Update",success:!0,message:"Successfully updated user profile"})}catch(e){d.push({test:"Profile Update",success:!1,error:e.message})}try{let{data:e}=await c.OQ.from("share_purchases").select("*").eq("user_id",u.id),{data:t}=await c.OQ.from("crypto_payment_transactions").select("*").eq("user_id",u.id),{data:s}=await c.OQ.from("commission_balances").select("*").eq("user_id",u.id).single(),r={sharesCount:e?.length||0,paymentsCount:t?.length||0,hasCommissionBalance:!!s,totalShares:e?.reduce((e,t)=>e+(t.shares_purchased||0),0)||0,totalInvested:e?.reduce((e,t)=>e+parseFloat(t.total_amount||0),0)||0,totalPayments:t?.reduce((e,t)=>e+parseFloat(t.amount||0),0)||0},a=.01>Math.abs(r.totalInvested-r.totalPayments);d.push({test:"Data Consistency Check",success:a,message:a?"Data is consistent across tables":"Data inconsistency detected",details:r})}catch(e){d.push({test:"Data Consistency Check",success:!1,error:e.message})}try{let e={user_id:u.id,test_type:"sync_validation",test_data:n,created_at:new Date().toISOString()},{error:t}=await c.OQ.from("sync_test_logs").insert(e);t&&"42P01"!==t.code?d.push({test:"Real-time Trigger Test",success:!1,error:t.message}):d.push({test:"Real-time Trigger Test",success:!0,message:"Successfully triggered real-time update"})}catch(e){d.push({test:"Real-time Trigger Test",success:!1,error:e.message})}try{let t=await Promise.all(["/api/shares","/api/payments","/api/referrals","/api/kyc/status"].map(async t=>{try{let s=await fetch(`${e.nextUrl.origin}${t}`,{headers:{Cookie:e.headers.get("cookie")||""}});return{endpoint:t,success:s.ok,status:s.status}}catch(e){return{endpoint:t,success:!1,error:e.message}}})),s=t.filter(e=>e.success).length,r=t.length;d.push({test:"API Endpoint Connectivity",success:s===r,message:`${s}/${r} endpoints accessible`,details:t})}catch(e){d.push({test:"API Endpoint Connectivity",success:!1,error:e.message})}try{let{error:e}=await c.OQ.rpc("test_transaction_integrity",{user_id_param:u.id});e&&"42883"!==e.code?d.push({test:"Transaction Integrity",success:!1,error:e.message}):d.push({test:"Transaction Integrity",success:!0,message:"Database transaction integrity verified"})}catch(e){d.push({test:"Transaction Integrity",success:!0,message:"Transaction integrity test skipped (function not available)"})}let l=d.filter(e=>e.success).length,h=d.length,m=l/h*100;return o.NextResponse.json({success:m>=80,successRate:m,totalTests:h,successfulTests:l,failedTests:h-l,tests:d,userInfo:{id:u.id,telegram_id:u.telegram_id,username:u.username},testMetadata:{timestamp:i,testData:n,executedAt:new Date().toISOString()},recommendations:function(e,t){let s=[];return t>=90?(s.push("Data synchronization is working excellently"),s.push("Continue monitoring for optimal performance")):t>=80?(s.push("Data synchronization is working well with minor issues"),s.push("Review failed tests and address any concerns")):t>=60?(s.push("Data synchronization has some issues that need attention"),s.push("Investigate failed tests and implement fixes")):(s.push("Data synchronization has significant issues"),s.push("Immediate attention required to fix critical problems")),e.filter(e=>!e.success).forEach(e=>{switch(e.test){case"Profile Update":s.push("Check database write permissions and connection");break;case"Data Consistency Check":s.push("Review data integrity and reconcile inconsistencies");break;case"Real-time Trigger Test":s.push("Verify real-time subscription setup and configuration");break;case"API Endpoint Connectivity":s.push("Check API endpoint availability and authentication");break;case"Transaction Integrity":s.push("Review database transaction handling and rollback mechanisms")}}),1===s.length&&s.push("Run sync tests regularly to maintain system health"),s}(d,m)})}catch(e){return console.error("Data sync test error:",e),o.NextResponse.json({error:"Failed to execute data sync test"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/data-sync/test/route",pathname:"/api/data-sync/test",filename:"route",bundlePath:"app/api/data-sync/test/route"},resolvedPagePath:"C:\\xampp\\htdocs\\aureus_africa\\app\\api\\data-sync\\test\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:d,staticGenerationAsyncStorage:l,serverHooks:h}=p,m="/api/data-sync/test/route";function g(){return(0,n.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:l})}},1066:(e,t,s)=>{"use strict";s.d(t,{OQ:()=>n});var r=s(2438);let a=(void 0).VITE_SUPABASE_URL||"https://fgubaqoftdeefcakejwu.supabase.co",i=(void 0).VITE_SUPABASE_ANON_KEY;if(!i)throw Error("Missing VITE_SUPABASE_ANON_KEY environment variable");let n=(0,r.eI)(a,i,{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}})}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[276,169],()=>s(7334));module.exports=r})();